FROM python:3.10-slim

RUN apt-get update
RUN apt-get install -y default-jre-headless

# Install Allure Commandline
WORKDIR /app
COPY . .
ENV ALLURE_VERSION=2.34.1
RUN tar -zxvf allure-${ALLURE_VERSION}.tgz && \
    mv allure-${ALLURE_VERSION} /opt/allure && \
    ln -s /opt/allure/bin/allure /usr/bin/allure && \
    rm allure-${ALLURE_VERSION}.tgz

ENV PATH="/opt/allure/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt

RUN apt-get install -y git
