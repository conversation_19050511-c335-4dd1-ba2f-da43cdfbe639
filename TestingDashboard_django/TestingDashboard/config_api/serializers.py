from rest_framework import serializers
from .models import BuildConfiguration, BoardConfiguration
from board_api.models import BoardInformation


class BuildConfigurationSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source='created_by.username', read_only=True)
    board_information_name = serializers.CharField(
        source='board_information.name', read_only=True)

    class Meta:
        model = BuildConfiguration
        fields = [
            'id', 'name', 'priority', 'description', 'board_information',
            'board_information_name', 'status', 'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name uniqueness"""
        if self.instance and self.instance.name == value:
            return value
        if BuildConfiguration.objects.filter(name=value).exists():
            raise serializers.ValidationError(
                "Build configuration name already exists")
        return value

    def update(self, instance, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().update(instance, validated_data)


class BuildConfigurationCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BuildConfiguration
        fields = ['name', 'priority', 'description',
                  'board_information', 'status']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class BoardConfigurationSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source='created_by.username', read_only=True)
    board_information_name = serializers.CharField(
        source='board_information.name', read_only=True)

    class Meta:
        model = BoardConfiguration
        fields = [
            'id', 'name', 'priority', 'description', 'board_information',
            'board_information_name', 'status', 'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name uniqueness"""
        if self.instance and self.instance.name == value:
            return value
        if BoardConfiguration.objects.filter(name=value).exists():
            raise serializers.ValidationError(
                "Board configuration name already exists")
        return value


class BoardConfigurationCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BoardConfiguration
        fields = ['name', 'priority', 'description',
                  'board_information', 'status']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Don't update created_by on update
        return super().update(instance, validated_data)
