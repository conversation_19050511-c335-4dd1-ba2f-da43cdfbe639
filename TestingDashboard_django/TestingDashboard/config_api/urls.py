from django.urls import path
from . import views

urlpatterns = [
    # Build Configuration
    path('build-configurations/', views.BuildConfigurationListCreateView.as_view(), name='build-configuration-list-create'),
    path('build-configurations/<int:pk>/', views.BuildConfigurationDetailView.as_view(), name='build-configuration-detail'),
    
    # Board Configuration
    path('board-configurations/', views.BoardConfigurationListCreateView.as_view(), name='board-configuration-list-create'),
    path('board-configurations/<int:pk>/', views.BoardConfigurationDetailView.as_view(), name='board-configuration-detail'),
    
    # Choices
    path('choices/', views.config_choices, name='config-choices'),
]
