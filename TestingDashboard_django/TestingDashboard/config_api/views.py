from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db import models
from .models import BuildConfiguration, BoardConfiguration
from .serializers import (
    BuildConfigurationSerializer, BuildConfigurationCreateSerializer,
    BoardConfigurationSerializer, BoardConfigurationCreateSerializer
)

# Build Configuration Views


class BuildConfigurationListCreateView(generics.ListCreateAPIView):
    queryset = BuildConfiguration.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            print(self.request.data)
            return BuildConfigurationCreateSerializer
        return BuildConfigurationSerializer

    def get_queryset(self):
        queryset = BuildConfiguration.objects.all()
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        priority_filter = self.request.query_params.get('priority', None)
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        board_info_filter = self.request.query_params.get(
            'board_information', None)
        if board_info_filter:
            queryset = queryset.filter(board_information=board_info_filter)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('name')


class BuildConfigurationDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = BuildConfiguration.objects.all()
    serializer_class = BuildConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def put(self, request, *args, **kwargs):
        try:
            return super().put(request, *args, **kwargs)
        except Exception as e:
            print(f"Error updating BuildConfiguration: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete build configurations'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)

# Board Configuration Views


class BoardConfigurationListCreateView(generics.ListCreateAPIView):
    queryset = BoardConfiguration.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BoardConfigurationCreateSerializer
        return BoardConfigurationSerializer

    def get_queryset(self):
        queryset = BoardConfiguration.objects.all()
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        priority_filter = self.request.query_params.get('priority', None)
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        board_info_filter = self.request.query_params.get(
            'board_information', None)
        if board_info_filter:
            queryset = queryset.filter(board_information=board_info_filter)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('name')


class BoardConfigurationDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = BoardConfiguration.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return BoardConfigurationCreateSerializer
        return BoardConfigurationSerializer

    def put(self, request, *args, **kwargs):
        try:
            return super().put(request, *args, **kwargs)
        except Exception as e:
            print(f"Error updating BoardConfiguration: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete board configurations'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def config_choices(request):
    """Get choices for configuration fields"""
    choices = {
        'status_choices': BuildConfiguration.STATUS_CHOICES,
    }
    return Response(choices)
