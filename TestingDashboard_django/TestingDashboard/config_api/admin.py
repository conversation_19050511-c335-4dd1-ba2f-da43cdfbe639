from django.contrib import admin
from .models import BuildConfiguration, BoardConfiguration


@admin.register(BuildConfiguration)
class BuildConfigurationAdmin(admin.ModelAdmin):
    list_display = ['name', 'priority', 'board_information',
                    'status', 'created_by', 'created_at']
    list_filter = ['priority', 'status', 'board_information', 'created_at']
    search_fields = ['name', 'board_information__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(BoardConfiguration)
class BoardConfigurationAdmin(admin.ModelAdmin):
    list_display = ['name', 'priority', 'board_information',
                    'status', 'created_by', 'created_at']
    list_filter = ['priority', 'status', 'board_information', 'created_at']
    search_fields = ['name', 'board_information__name']
    readonly_fields = ['created_at', 'updated_at']
