from django.db import models
from django.contrib.auth.models import User
from board_api.models import BoardInformation


class BuildConfiguration(models.Model):
    # PRIORITY_CHOICES = [
    #     ('Low', 'Low'),
    #     ('Medium', 'Medium'),
    #     ('High', 'High'),
    #     ('Critical', 'Critical'),
    # ]

    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Inactive', 'Inactive'),
        ('Draft', 'Draft'),
    ]

    name = models.CharField(max_length=200, unique=True)
    priority = models.IntegerField(default=1)
    description = models.JSONField(
        default=dict, blank=True)  # JSON description
    board_information = models.ForeignKey(
        BoardInformation, on_delete=models.CASCADE, related_name='build_configs')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='Active')
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_build_configs')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.board_information.name})"


class BoardConfiguration(models.Model):
    # PRIORITY_CHOICES = [
    #     ('Low', 'Low'),
    #     ('Medium', 'Medium'),
    #     ('High', 'High'),
    #     ('Critical', 'Critical'),
    # ]

    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Inactive', 'Inactive'),
        ('Draft', 'Draft'),
    ]

    name = models.CharField(max_length=200, unique=True)
    priority = models.IntegerField(default=100)
    description = models.JSONField(
        default=dict, blank=True)  # JSON description
    board_information = models.ForeignKey(
        BoardInformation, on_delete=models.CASCADE, related_name='board_configs')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='Active')
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_board_configs')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.board_information.name})"
