from rest_framework import serializers
from .models import TestCase
from board_api.models import BoardInformation


class TestCaseSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source='created_by.username', read_only=True)
    platform_names = serializers.SerializerMethodField()
    platform_ids = serializers.SerializerMethodField()

    class Meta:
        model = TestCase
        fields = [
            'id', 'testcase_id', 'brief', 'details', 'pre_condition', 'post_condition',
            'test_level', 'test_type', 'test_technique', 'test_procedure', 'pass_criteria',
            'example_log', 'note', 'requirements', 'platform', 'platform_ids', 'platform_names',
            'hw_depend', 'module', 'suite', 'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_platform_names(self, obj):
        """Get platform names"""
        return obj.get_platform_names()

    def get_platform_ids(self, obj):
        """Get platform IDs for backward compatibility"""
        return [platform.id for platform in obj.platform.all()]

    def validate_testcase_id(self, value):
        """Validate testcase_id uniqueness"""
        if self.instance and self.instance.testcase_id == value:
            return value
        if TestCase.objects.filter(testcase_id=value).exists():
            raise serializers.ValidationError("Test case ID already exists")
        return value

    def validate_estimated_time(self, value):
        """Validate estimated time is positive"""
        if value <= 0:
            raise serializers.ValidationError(
                "Estimated time must be greater than 0")
        return value


class TestCaseCreateSerializer(serializers.ModelSerializer):
    # Make most fields optional for creation
    brief = serializers.CharField(required=False, allow_blank=True)
    details = serializers.CharField(required=False, allow_blank=True)
    pre_condition = serializers.CharField(required=False, allow_blank=True)
    post_condition = serializers.CharField(required=False, allow_blank=True)
    test_procedure = serializers.CharField(required=False, allow_blank=True)
    pass_criteria = serializers.CharField(required=False, allow_blank=True)
    example_log = serializers.CharField(required=False, allow_blank=True)
    note = serializers.CharField(required=False, allow_blank=True)
    requirements = serializers.CharField(required=False, allow_blank=True)
    hw_depend = serializers.CharField(required=False, allow_blank=True)

    platform = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=BoardInformation.objects.all(),
        required=False
    )

    class Meta:
        model = TestCase
        fields = [
            'testcase_id', 'brief', 'details', 'pre_condition', 'post_condition',
            'test_level', 'test_type', 'test_technique', 'test_procedure', 'pass_criteria',
            'example_log', 'note', 'requirements', 'platform', 'hw_depend', 'module', 'suite'
        ]
        extra_kwargs = {
            'testcase_id': {'required': True},  # Only testcase_id is required
            'test_level': {'required': False},
            'test_type': {'required': False},
            'test_technique': {'required': False},
        }

    def validate_testcase_id(self, value):
        """Validate testcase_id uniqueness"""
        if TestCase.objects.filter(testcase_id=value).exists():
            raise serializers.ValidationError("Test case ID already exists")
        return value

    def create(self, validated_data):
        # Extract platform data for ManyToMany field
        platform_data = validated_data.pop('platform', [])

        # Set default values for optional fields if not provided
        validated_data.setdefault('brief', '')
        validated_data.setdefault('details', '')
        validated_data.setdefault('pre_condition', '')
        validated_data.setdefault('post_condition', '')
        validated_data.setdefault('test_procedure', '')
        validated_data.setdefault('pass_criteria', '')
        validated_data.setdefault('example_log', '')
        validated_data.setdefault('note', '')
        validated_data.setdefault('requirements', '')
        validated_data.setdefault('hw_depend', '')
        validated_data.setdefault('test_level', 'System')
        validated_data.setdefault('test_type', 'Functional')
        validated_data.setdefault('test_technique', 'BlackBox')

        validated_data['created_by'] = self.context['request'].user

        # Create the instance
        instance = super().create(validated_data)

        # Set the ManyToMany relationships
        if platform_data:
            instance.platform.set(platform_data)

        return instance


class TestCaseUpdateSerializer(serializers.ModelSerializer):
    platform = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=BoardInformation.objects.all(),
        required=False
    )

    class Meta:
        model = TestCase
        fields = [
            'brief', 'details', 'pre_condition', 'post_condition', 'test_level',
            'test_type', 'test_technique', 'test_procedure', 'pass_criteria',
            'example_log', 'note', 'requirements', 'platform', 'hw_depend', 'module', 'suite'
        ]

    def update(self, instance, validated_data):
        # Extract platform data for ManyToMany field
        platform_data = validated_data.pop('platform', None)

        # Update other fields
        instance = super().update(instance, validated_data)

        # Update ManyToMany relationships if provided
        if platform_data is not None:
            instance.platform.set(platform_data)

        return instance
