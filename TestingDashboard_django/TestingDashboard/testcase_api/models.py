from django.db import models
from django.contrib.auth.models import User
import json


class TestCase(models.Model):
    TEST_LEVEL_CHOICES = [
        ('Unit', 'Unit'),
        ('Integration', 'Integration'),
        ('System', 'System'),
        ('Acceptance', 'Acceptance'),
    ]

    TEST_TYPE_CHOICES = [
        ('Functional', 'Functional'),
        ('Non-Functional', 'Non-Functional'),
        ('Performance', 'Performance'),
        ('Security', 'Security'),
        ('Usability', 'Usability'),
        ('Compatibility', 'Compatibility'),
    ]

    TEST_TECHNIQUE_CHOICES = [
        ('BlackBox', 'Black Box'),
        ('WhiteBox', 'White Box'),
        ('GreyBox', 'Grey Box'),
    ]

    testcase_id = models.CharField(max_length=100, unique=True)
    brief = models.TextField(blank=True)
    details = models.TextField(blank=True)
    pre_condition = models.TextField(blank=True)
    post_condition = models.TextField(blank=True)
    test_level = models.CharField(
        max_length=50, choices=TEST_LEVEL_CHOICES, default='System')
    test_type = models.CharField(
        max_length=50, choices=TEST_TYPE_CHOICES, default='Functional')
    test_technique = models.CharField(
        max_length=50, choices=TEST_TECHNIQUE_CHOICES, default='BlackBox')
    test_procedure = models.TextField(blank=True)
    pass_criteria = models.TextField(blank=True)
    example_log = models.TextField(blank=True)
    note = models.TextField(blank=True)
    requirements = models.TextField(blank=True)
    platform = models.ManyToManyField(
        'board_api.BoardInformation',
        blank=True,
        related_name='testcases',
        help_text="Board platforms this test case applies to"
    )
    hw_depend = models.TextField(
        blank=True, help_text="Hardware dependencies description")
    module = models.CharField(max_length=200, blank=True)
    suite = models.CharField(max_length=200, blank=True)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_testcases')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['testcase_id']

    def __str__(self):
        return f"{self.testcase_id} - {self.brief[:50] if self.brief else 'No brief'}"

    def get_platform_names(self):
        """Helper method to get platform names"""
        return [platform.name for platform in self.platform.all()]
