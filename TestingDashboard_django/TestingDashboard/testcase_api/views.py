import os
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db import models
from common import utils
from common.tc_parsers import DataStructure, LtpParser
from .models import TestCase, User
from .serializers import TestCaseSerializer, TestCaseCreateSerializer, TestCaseUpdateSerializer
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt


class OptionalPagination(PageNumberPagination):
    page_size = 20

    def paginate_queryset(self, queryset, request, view=None):
        if request.query_params.get('all') == 'true':
            return None
        return super().paginate_queryset(queryset, request, view)


class TestCaseListCreateView(generics.ListCreateAPIView):
    queryset = TestCase.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = OptionalPagination

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TestCaseCreateSerializer
        return TestCaseSerializer

    def get_queryset(self):
        # queryset = TestCase.objects.all()
        queryset = super().get_queryset()

        # Filter by specific module
        module = self.request.query_params.get('module', None)
        if module:
            queryset = queryset.filter(module__iexact=module)

        # Filter by specific suite
        suite = self.request.query_params.get('suite', None)
        if suite:
            queryset = queryset.filter(suite__iexact=suite)

        # Search by module or suite or testcase_id (general search)
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                models.Q(suite__icontains=search) |
                models.Q(module__icontains=search) |
                models.Q(testcase_id__icontains=search)
            )
        return queryset.order_by('testcase_id')


class TestCaseDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = TestCase.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return TestCaseUpdateSerializer
        return TestCaseSerializer

    def delete(self, request, *args, **kwargs):
        # Only admin can delete test cases
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete test cases'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)


class SyncLtpTestcases(APIView):
    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        # Parse testcases
        logger = utils.setup_logger(
            f'SyncLtpTestcases', "sync_ltp_tc.log", reset_logfile=True)
        data = DataStructure()
        ltp = LtpParser(data=data, logger=logger)
        ltp.parse_tc()
        logger.info(f"number parsed testcase ltp {len(data.test_infos)}")

        # Update or create in DB
        for tc in data.test_infos:
            testcase, created = TestCase.objects.update_or_create(
                testcase_id=tc,
                defaults={
                    'brief': data.get_desc(tc),
                    'details': data.get_desc(tc),
                    'module': data.get_path(tc),
                    'suite': data.get_suite(tc),
                    'created_by': User.objects.get(username="admin"),
                }
            )
            if created:
                logger.info(f'Created new TestCase {tc}')
            else:
                logger.info(f'Updated existing TestCase {tc}')

        return Response({"message": "Testcases synced successfully"}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def testcase_choices(request):
    """Get choices for test case fields"""
    choices = {
        'priority_choices': TestCase.PRIORITY_CHOICES,
        'status_choices': TestCase.STATUS_CHOICES,
        'test_level_choices': TestCase.TEST_LEVEL_CHOICES,
    }
    return Response(choices)


@csrf_exempt
@api_view(["POST"])
@permission_classes([AllowAny])
@authentication_classes([])
def sync_ltp_testcases(request):
    # Parse testcases
    logger = utils.setup_logger(
        f'SyncLtpTestcases', "sync_ltp_tc.log", reset_logfile=True)
    data = DataStructure()
    ltp = LtpParser(data=data, logger=logger)
    ltp.parse_tc()
    logger.info(f"number parsed testcase ltp {len(data.test_infos)}")

    # Update or create in DB
    for tc in data.test_infos:
        testcase, created = TestCase.objects.update_or_create(
            testcase_id=tc,
            defaults={
                'brief': data.get_desc(tc),
                'details': data.get_desc(tc),
                'module': data.get_path(tc),
                'suite': data.get_suite(tc),
                'created_by': User.objects.get(username="admin"),
            }
        )
        if created:
            logger.info(f'Created new TestCase {tc}')
        else:
            logger.info(f'Updated existing TestCase {tc}')

    return Response({"message": "Testcases synced successfully"}, status=status.HTTP_200_OK)
