import uuid
import json
import os
import re
import shutil
import time
import zipfile
from datetime import datetime

from common.jen<PERSON> import JenkinsAP<PERSON>
from common.utils import setup_logger, TMP_DIR, DATA_DIR, ALLURE_DIR
from common.tc_parsers import DataStructure, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from testing_plan_api.models import TestingPlan, TestingPlanBatch, TestingPlanTotal, TestingPlanRun, Result
from testcase_api.models import TestCase
from testing_plan_api.models import AllureReport


class TestcaseResult:
    def __init__(self):
        pass

    @staticmethod
    def sync_test_result(data):
        tplan_run_id, device = data
        logger = setup_logger(
            f'sync_test_result', f"sync_test_result/{tplan_run_id}/sync.log", reset_logfile=True)
        logger.info("sync_test_result")
        tplan_run_obj = TestingPlanRun.objects.get(pk=tplan_run_id)
        if not tplan_run_obj:
            logger.error(f"Can't find testing plan run with id {tplan_run_id}")
        # sync test result from pytest xml
        pytest_result = os.path.join(
            TMP_DIR, str(tplan_run_id), "pytest_res.xml")
        pytest_result_status = JenkinsAPI.get_pytest_result(
            pytest_result, str(tplan_run_obj.jenkins_build_number))
        if not pytest_result_status or not os.path.exists(pytest_result):
            logger.error(
                f"Can't get pytest result from Jenkins test job id {tplan_run_obj.jenkins_build_number} and local file {pytest_result} not found")
            return
        logger.info(f"Successfully get pytest result to {pytest_result}")
        data = DataStructure()
        board_name = tplan_run_obj.testing_plan_batch.testing_plan.board_information.name
        PytestParser(data, logger).parse_results(pytest_result, board_name)

        # sync test result from ltp result
        ltp_result = os.path.join(TMP_DIR, str(tplan_run_id), "ltp-results")
        ltp_zip = os.path.join(TMP_DIR, str(tplan_run_id), "ltp_result.zip")
        ltp_result_status = JenkinsAPI.get_ltp_result(
            ltp_zip, str(tplan_run_obj.jenkins_build_number))
        if not ltp_result_status or not os.path.exists(ltp_zip):
            logger.error(
                f"Can't get ltp result from Jenkins test job id {tplan_run_obj.jenkins_build_number} and local file {ltp_zip} not found")
        else:
            logger.info(f"Successfully get ltp result to {ltp_zip}")
            with zipfile.ZipFile(ltp_zip, 'r') as zip_ref:
                zip_ref.extractall(ltp_result)
            data = DataStructure()
            board_name = tplan_run_obj.testing_plan_batch.testing_plan.board_information.name
            LtpParser(data, logger).parse_results(ltp_result, board_name)

        shutil.rmtree(ltp_result, ignore_errors=True)

        for tc in data.test_infos:
            result = data.get_result(tc).get(board_name, "NotTest")
            logger.info(f"Adding result {tc} - {result}")
            try:
                tc_obj = TestCase.objects.get(testcase_id=tc)
            except:
                logger.error(f"Testcase {tc} not found in database")
                continue
            result_obj = Result.objects.create(
                testing_plan_run=tplan_run_obj,
                test_case=tc_obj,
                result=result
            )
            logger.info(f"Added result {tc} - {result}")

        tplan_total_id = tplan_run_obj.testing_plan_batch.testing_plan.testing_plan_total.pk
        tplan_total_infor = tplan_run_obj.testing_plan_batch.testing_plan.testing_plan_total.information
        tplan_tag_name = re.findall(r"Tag=(.*)", tplan_total_infor)
        tplan_tag_name = tplan_tag_name[0] if tplan_tag_name else tplan_run_obj.testing_plan_batch.testing_plan.testing_plan_total.name
        tplan_prev_tag_name = re.findall(r"Prev_tag=(.*)", tplan_total_infor)
        tplan_prev_tag_name = tplan_prev_tag_name[0] if tplan_prev_tag_name else ""
        allure_res_object = AllureResult(
            tplan_total_id, tplan_tag_name, tplan_prev_tag_name, board_name, logger)
        # Convert ltp result to allure result
        ltp_idir = os.path.join(TMP_DIR, str(tplan_run_id), "ltp_idir")
        ltp_zip = os.path.join(TMP_DIR, str(tplan_run_id), "ltp_result.zip")
        ltp_odir = os.path.join(TMP_DIR, str(tplan_run_id), "ltp_odir")
        ltp_zip_status = JenkinsAPI.get_ltp_result(
            ltp_zip, str(tplan_run_obj.jenkins_build_number))
        if not ltp_zip_status or not os.path.exists(ltp_zip):
            logger.error(
                f"Can't get ltp result from Jenkins test job id {tplan_run_obj.jenkins_build_number} and local file {ltp_zip} not found")
        else:
            logger.info(
                f"Successfully get ltp result to {ltp_zip} for device {device}")
            with zipfile.ZipFile(ltp_zip, 'r') as zip_ref:
                zip_ref.extractall(ltp_idir)

            allure_res_object.ltp_convert(ltp_idir, ltp_odir)
            allure_res_object.merge_report(
                ltp_odir, tplan_run_obj.jenkins_url, device)

        shutil.rmtree(ltp_idir, ignore_errors=True)
        shutil.rmtree(ltp_odir, ignore_errors=True)

        # merge allure report for tplan_total
        allure_result = os.path.join(
            TMP_DIR, str(tplan_run_id), "allure-results")
        allure_zip = os.path.join(TMP_DIR, str(
            tplan_run_id), "allure-results.zip")
        allure_result_status = JenkinsAPI.get_allure_result(
            allure_zip, str(tplan_run_obj.jenkins_build_number))
        if not allure_result_status or not os.path.exists(allure_zip):
            logger.error(
                f"Can't get allure result from Jenkins test job id {tplan_run_obj.jenkins_build_number} and local file {allure_zip} not found")
        else:
            logger.info(
                f"Successfully get allure result to {allure_zip} for device {device}")
            with zipfile.ZipFile(allure_zip, 'r') as zip_ref:
                zip_ref.extractall(os.path.dirname(allure_result))

            allure_res_object.merge_report(
                allure_result, tplan_run_obj.jenkins_url, device)

        shutil.rmtree(allure_result, ignore_errors=True)

        allure_res_object.gen_report()


class AllureResult:
    def __init__(self, tplan_total_id, tplan_tag_name, tplan_prev_tag_name, tplan_conf, logger):
        tplan_tag_folder = re.sub(r"\s+|\[|\]", "", tplan_tag_name)
        tplan_prev_tag_folder = re.sub(r"\s+|\[|\]", "", tplan_prev_tag_name)
        self.history_dir = os.path.join(
            DATA_DIR, tplan_prev_tag_folder, tplan_conf, "report", "history")
        self.result_history_dir = os.path.join(
            DATA_DIR, tplan_tag_folder, tplan_conf, "result", "history")
        self.result_dir = os.path.join(
            DATA_DIR, tplan_tag_folder, tplan_conf, "result")
        self.allure_result_dir = os.path.join(
            ALLURE_DIR, tplan_tag_folder, tplan_conf, "allure-results", tplan_tag_name)
        # os.path.join(DATA_DIR, tplan_tag_folder, tplan_conf, "report")
        self.report_dir = os.path.join(
            DATA_DIR, tplan_tag_folder, tplan_conf, "report")

        shutil.rmtree(self.result_history_dir, ignore_errors=True)
        if tplan_prev_tag_name and os.path.exists(self.history_dir):
            shutil.copytree(self.history_dir, self.result_history_dir)

        os.makedirs(self.result_dir, exist_ok=True)
        env_data = [f"Tag = {tplan_tag_name}",
                    f"TplanID = {tplan_total_id}", f"Board = {tplan_conf}"]
        env_file = os.path.join(self.result_dir, "environment.properties")
        with open(env_file, "w") as env_fp:
            env_fp.write("\n".join(env_data))

        exec_data = {"name": "Jenkins", "type": "jenkins"}
        exec_file = os.path.join(self.result_dir, "executor.json")
        with open(exec_file, "w") as exec_fp:
            json.dump(exec_data, exec_fp, indent=4)

        if not logger:
            self.logger = setup_logger(f'AllureResult', 'AllureResult.txt')
        else:
            self.logger = logger

    def ltp_convert(self, ltp_result_idir, allure_result_odir):
        def parse_ltp_log(log_path):
            testcases = []
            start_time = None
            hostname = "unknown"

            with open(log_path, 'r', encoding="utf-8", errors='ignore') as f:
                lines = f.readlines()

            for line in lines:
                if line.startswith("Test Start Time:"):
                    start_time_str = line.split("Test Start Time:")[1].strip()
                    start_time = int(time.mktime(datetime.strptime(
                        start_time_str, "%a %b %d %H:%M:%S %Y").timetuple()) * 1000)
                elif line.startswith("Hostname:"):
                    hostname = line.split("Hostname:")[1].strip()
                elif line.strip() and not line.startswith("-") and not line.startswith("Testcase") and not line.startswith("Total"):
                    parts = line.split()
                    if len(parts) >= 3:
                        name = parts[0]
                        result = parts[1].lower()
                        exit_code = parts[2]
                        testcases.append({
                            "name": name,
                            "status": "passed" if result == "pass" else "failed" if result == "fail" else "skipped",
                            "exit_code": exit_code
                        })

            return testcases, start_time or int(time.time() * 1000), hostname

        def generate_allure_results(testcases, sub_suite, start_time, hostname, result_dir):
            for tc in testcases:
                uid = str(uuid.uuid4())
                result = {
                    "uuid": uid,
                    "name": tc["name"],
                    "status": tc["status"],
                    "start": start_time,
                    "stop": start_time + 10000,
                    "labels": [
                        {"name": "framework", "value": "ltp"},
                        {"name": "host", "value": hostname},
                        {"name": "suite", "value": "ltp"},
                        {"name": "subSuite", "value": sub_suite}
                    ],
                    "statusDetails": {
                        "message": f"Exit code: {tc['exit_code']}"
                    }
                }
                with open(os.path.join(result_dir, f"{uid}-result.json"), 'w') as f:
                    json.dump(result, f, indent=2)

        os.makedirs(allure_result_odir, exist_ok=True)
        for root, _dirs, _files in os.walk(ltp_result_idir):
            for _file in _files:
                # Parse Results
                if _file.endswith('.log'):
                    filepath = os.path.join(root, _file)
                    sub_suite = _file.replace(".log", "").split("_")[-1]
                    testcases, start_time, hostname = parse_ltp_log(filepath)
                    generate_allure_results(
                        testcases, sub_suite, start_time, hostname, allure_result_odir)
                    self.logger.info(
                        f"Generated {len(testcases)} Allure result files in '{allure_result_odir}'")

    def merge_report(self, merge_dir, jenkins_url, device_id):
        def parse_allure_report(allure_dir):
            parse_result = os.path.join(allure_dir, "result.parsed")
            if os.path.exists(parse_result):
                with open(parse_result, 'r', encoding='utf-8') as f:
                    return json.load(f)

            allure_result = dict()
            for filename in os.listdir(allure_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(allure_dir, filename)
                    with open(filepath, 'r', encoding='utf-8') as f:
                        try:
                            data = json.load(f)
                            name = data.get('name')
                            status = data.get('status')
                            if name and status:
                                allure_result[name] = {
                                    'source': filepath,
                                    'status': status
                                }
                        except Exception as e:
                            self.logger.error(f"Error reading {filepath}: {e}")
            with open(parse_result, "w") as json_file:
                json.dump(allure_result, json_file, indent=4)
            return allure_result

        def add_meta_data(jenkins_url, device_id, allure_tc_fp):
            with open(allure_tc_fp, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Jenkins link
            link_entry = {
                "name": f"Click here to {jenkins_url}",
                "type": "jenkins",
                "url": jenkins_url
            }

            if "links" in data and isinstance(data["links"], list):
                data["links"].append(link_entry)
            else:
                data["links"] = [link_entry]

            # Device id
            device_entry = {
                "name": "tag",
                "value": device_id
            }

            if "labels" in data and isinstance(data["labels"], list):
                data["labels"].append(device_entry)
            else:
                data["labels"] = [device_entry]

            with open(allure_tc_fp, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)

        # Status priority: lower index means better result
        status_priority = ['passed', 'skipped', 'failed', 'broken']
        merge_results = {}  # Dictionary to hold results need to merge by test name

        # Get target report data
        best_results = parse_allure_report(self.result_dir)
        # Iterate through merge_dir directories
        for filename in os.listdir(merge_dir):
            filepath = os.path.join(merge_dir, filename)
            if filename.endswith('-result.json'):
                with open(filepath, 'r', encoding='utf-8') as f:
                    try:
                        data = json.load(f)
                        name = data.get('name')
                        status = data.get('status')
                        if name and status:
                            current_best = best_results.get(name)
                            if (not current_best or
                                    status_priority.index(status) <= status_priority.index(current_best['status'])):
                                self.logger.info(
                                    f"Found better result of tc {name} - {status} in {filepath}")
                                # Save new result
                                saved_result_path = os.path.join(
                                    self.result_dir, filename)
                                best_results[name] = {
                                    'source': saved_result_path,
                                    'status': status
                                }
                                # Need copy result
                                merge_results[name] = {
                                    'source': filepath,
                                }
                                # Remove old result
                                if current_best and current_best['source'] != saved_result_path:
                                    self.logger.info(
                                        f"Remove old result in {current_best['source']}")
                                    os.remove(current_best["source"])

                    except Exception as e:
                        self.logger.error(f"Error reading {filepath}: {e}")
            else:
                # Copy all attachments, setup, teardown steps to target dir
                merge_data = os.path.join(self.result_dir, filename)
                shutil.copy2(filepath, merge_data)

        # Copy best result files to self.result_dir directory
        self.logger.info(f"starting merge ...")
        for name, result in merge_results.items():
            need_merge_tc = result['source']
            if self.result_dir in need_merge_tc:
                self.logger.info(
                    f"{need_merge_tc} already in {self.result_dir}")
                continue
            merge_tc = os.path.join(
                self.result_dir, os.path.basename(need_merge_tc))
            shutil.copy2(need_merge_tc, merge_tc)
            add_meta_data(jenkins_url, device_id, merge_tc)
            self.logger.info(f"Merged tc {name}")

        parse_result = os.path.join(self.result_dir, "result.parsed")
        with open(parse_result, "w") as json_file:
            json.dump(best_results, json_file, indent=4)

        self.logger.info(
            f"Merged {len(merge_results)} best test results into '{self.result_dir}' directory.")

    def gen_report(self):
        shutil.rmtree(self.allure_result_dir, ignore_errors=True)
        os.makedirs(self.allure_result_dir, exist_ok=True)
        os.system(
            f"allure generate {self.result_dir} --clean -o {self.allure_result_dir}")
        allure_obj = AllureReport.objects.create(
            testing_plan_total=self.testing_plan_total,
            report_file=f"/allure-report/{self.testing_plan_total.id}"
        )
        allure_obj.save()
