import os
import re
import shutil
import xml.etree.ElementTree as et

from common import utils
from django.conf import settings

class DataStructure(object):
    def __init__(self):
        self.test_infos = {}
        self.module_lst = []
        self.board_list = []
        self.fail_list = []
        self.file_name = 'Linux_BSP_Test_Report'
        self.test_config = {'JOB_NAME':'JOB_NAME',
                            'BUILD_NUMBER':'BUILD_NUMBER',
                            'BUILD_URL':'BUILD_URL',
                            'BUILD_TIMESTAMP':'BUILD_TIMESTAMP',
                            'BUILD_DURATION':'BUILD_DURATION',
                            'BOARD_TYPE':'BOARD_TYPE',
                            'KERNEL_CONFIG':'KERNEL_CONFIG',
                            'UBOOT_CONFIG':'UBOOT_CONFIG',
                            'ROOTFS_CONFIG':'ROOTFS_CONFIG',
                            'CUSTOM_ARGS':'CUSTOM_ARGS',
                            'RELEASE_DATE':'RELEASE_DATE',
                            'BOARDS_VER':'BOARDS_VER',
                            'PC_CONF':'PC_CONF',
                            'CHIP_VER':'CHIP_VER',
                            'TOOCHAIN':'TOOCHAIN',
                            'UBOOT_VER':'UBOOT_VER',
                            'LINUX_VER':'LINUX_VER',
                            'ROOTFS_VER':'ROOTFS_VER',
                            'PRODUCT_NAME':'PRODUCT_NAME',
                            'REL_VER':'REL_VER',
                            }
        self.abnormal = {}

    @staticmethod
    def __template_data():
        # Test_Data = {<Test_ID>:<INFO>, ...}
        # INFO = {<Test_Desc:       'str'>,
        #         <Test_Type:       'str'>,
        #         <Test_Ticket:     {<{IssueStatus:,IssueID:,Reporter:,Assignee:,CreatedDate:,UpdatedDate:,IssueURL:}>} >,
        #         <Test_Result:     {<Board>:[Results], ...}>,
        #         <Test_Tester:     'str'>,
        #         <Test_Comments:   'str'>,
        #         <Test_Path:       'str' >,
        #         <Test_Log:        {<Board>:[Logs], ...}>,
        #         ...}

        __tmp_INFO = {}
        __tmp_INFO['Test_Desc'] = ''
        __tmp_INFO['Test_Type'] = ''
        __tmp_INFO['Test_Ticket'] = {}
        __tmp_INFO['Test_Result'] = {}
        __tmp_INFO['Test_Tester'] = {}
        __tmp_INFO['Test_Comments'] = {}
        __tmp_INFO['Test_Path'] = ''
        __tmp_INFO['Test_OS'] = ''
        __tmp_INFO['Test_Suite'] = ''
        __tmp_INFO['Test_Log'] = {}
        __tmp_INFO['Test_Reqs'] = []
        __tmp_INFO['Test_Platforms'] = []
        __tmp_INFO['Build_config'] = []
        __tmp_INFO['time_hw_setup'] = 0
        __tmp_INFO['time_setup'] = 0
        __tmp_INFO['time_test'] = 0
        __tmp_INFO['config_plan'] = ''
        __tmp_INFO['test_fw'] = []
        return __tmp_INFO

    def new_tc_id(self, test_id):
        if not self.__is_tc_exist(test_id, warn=False):
            self.test_infos[test_id] = self.__template_data().copy()
            return True
        else:
            return False

    def __is_tc_exist(self, test_id, warn=True):
        if test_id not in self.test_infos:
            if 'ltp' in test_id or \
               'duts' in test_id:
                return False
            if warn is True:
                return False
            return False
            # self.new_tc_id(test_id)
            # return True
        return True

    def get_desc(self, test_id):
        return self.test_infos[test_id]['Test_Desc']

    def get_type(self, test_id):
        if test_id not in self.test_infos:
            return None
        return self.test_infos[test_id].get('Test_Type', None)

    def get_ticket(self, test_id):
        return self.test_infos[test_id]['Test_Ticket']

    def get_text_tickets(self, test_id):
        text = ""
        for board in self.test_infos[test_id]['Test_Ticket']:
            ticket_list = []
            for ticket in self.test_infos[test_id]['Test_Ticket'][board]:
                ticket_list.append(ticket)
            if len(ticket_list) > 0:
                if len(self.test_infos[test_id]['Test_Ticket']) == 1:
                    text = ', '.join(ticket_list)
                else:
                    text += f"{board}: {', '.join(ticket_list)}\n"
        return text

    def get_result(self, test_id):
        return self.test_infos[test_id]['Test_Result']
    
    def get_result_board(self, test_id, board):
        if test_id not in self.test_infos or board not in self.test_infos[test_id]['Test_Result']:
            return None
        return self.test_infos[test_id]['Test_Result'][board]

    def get_tester(self, test_id):
        return self.test_infos[test_id]['Test_Tester']

    def get_tester_board(self, test_id, board):
        if test_id not in self.test_infos or board not in self.test_infos[test_id]['Test_Tester']:
            return None
        return self.test_infos[test_id]['Test_Tester'][board]

    def get_text_testers(self, test_id):
        text = ""
        for board, tester in self.test_infos[test_id]['Test_Tester'].items():
            if tester != "":
                if len(self.test_infos[test_id]['Test_Tester']) == 1:
                    text = tester
                else:
                    text += f"{board}: {tester}\n"
        return text

    def get_comments(self, test_id):
        return self.test_infos[test_id]['Test_Comments']

    def get_comment_board(self, test_id, board):
        if test_id not in self.test_infos or board not in self.test_infos[test_id]['Test_Comments']:
            return None
        return self.test_infos[test_id]['Test_Comments'][board]

    def get_text_comments(self, test_id):
        text = ""
        for board, comment in self.test_infos[test_id]['Test_Comments'].items():
            if comment != "":
                if len(self.test_infos[test_id]['Test_Comments']) == 1:
                    text = comment
                else:
                    text += f"{board}: {comment}\n"
        return text

    def get_path(self, test_id):
        return self.test_infos[test_id]['Test_Path']

    def get_os(self, test_id):
        return self.test_infos[test_id]['Test_OS']

    def get_suite(self, test_id):
        return self.test_infos[test_id]['Test_Suite']

    def get_log(self, test_id):
        return self.test_infos[test_id]['Test_Log']

    def get_requirements(self, test_id):
        if len(self.test_infos[test_id]['Test_Reqs']) > 0:
            return self.test_infos[test_id]['Test_Reqs']
        else:
            return ['ALB_COMUNITY']

    def get_platforms(self, test_id):
        if len(self.test_infos[test_id]['Test_Platforms']) > 0:
            return self.test_infos[test_id]['Test_Platforms']
        else:
            return ['ALB_GENERIC']

    def get_build_config(self, test_id):
        return self.test_infos[test_id]['Build_config']

    def get_time_hw_setup(self, test_id):
        return self.test_infos[test_id]['time_hw_setup']

    def get_time_setup(self, test_id):
        return self.test_infos[test_id]['time_setup']

    def get_time_test(self, test_id):
        return self.test_infos[test_id]['time_test']

    def get_config_plan(self, test_id):
        return self.test_infos[test_id]['config_plan']

    def get_test_fw(self, test_id):
        return ", ".join(self.test_infos[test_id]['test_fw'])

    def get_file_name(self):
        return self.file_name

    def get_tc_list(self):
        return self.test_infos

    def get_board_list(self):
        if self.board_list:
            return self.board_list
        else:
            return ['DUMMY_BOARD']

    def get_module_list(self):
        return self.module_lst

    def get_fail_list(self):
        return self.fail_list

    def get_config(self):
        return self.test_config

    def compare_testcase_data(self, tc_obj, tc_compare):
        if "TestTime" in tc_compare:
            return False

        check_list = ["Description", "Type", "Module", "Os", "Suite", "Name"]
        for key in check_list:
            if getattr(tc_obj, key) != tc_compare[key]:
                return False
        tc_req = [ r.Id for r in tc_obj.Requirement ]
        tc_plat = [ r.Id for r in tc_obj.Platforms ]
        if tc_req != tc_compare["Requirement"] or tc_plat != tc_compare["Platforms"]:
            return False

        return True

    def update_desc(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Desc'] = info

    def update_type(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Type'] = info

    def update_ticket(self, test_id, board, info):
        if self.__is_tc_exist(test_id):
            issue_id = info['IssueID']
            if board not in self.test_infos[test_id]['Test_Ticket']:
                self.test_infos[test_id]['Test_Ticket'][board] = {}
            self.test_infos[test_id]['Test_Ticket'][board][issue_id] = info

    def update_result(self, test_id, info):
        if self.__is_tc_exist(test_id):
            for board in info:
                if board not in self.board_list:
                    self.board_list.append(board)
                if info[board] == 'FAIL' and test_id not in self.fail_list:
                    self.fail_list.append(test_id)
            self.test_infos[test_id]['Test_Result'].update(info)
        else:
            if 'ltp' in test_id or 'duts' in test_id and 'FAIL' in info.values():
                for board in info:
                    if info[board] == 'FAIL':
                         self.abnormal[board]=True

    def update_tester(self, test_id, board, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Tester'][board] = info

    def update_comments(self, test_id, board, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Comments'][board]= info

    def update_path(self, test_id, info):
        if self.__is_tc_exist(test_id):
            module = re.search(r'[\\/](\w+)(?=[\\/])', info, flags=re.I).group(1).upper()
            if module not in self.module_lst and info != '':
                self.module_lst.append(module)
            self.test_infos[test_id]['Test_Path'] = info

    def update_os(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_OS'] = info

    def update_suite(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Suite'] = info

    def update_log(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Log'].update(info)

    def update_requirements(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Reqs'] = info

    def update_platforms(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Test_Platforms'] = info

    def update_build_config(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['Build_config'] = info

    def update_time_hw_setup(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['time_hw_setup'] = info

    def update_time_setup(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['time_setup'] = info

    def update_time_test(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['time_test'] = info

    def update_config_plan(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['config_plan'] = info

    def update_test_fw(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['test_fw'] = info

    def append_test_fw(self, test_id, info):
        if self.__is_tc_exist(test_id):
            self.test_infos[test_id]['test_fw'].append(info)

    def update_file_name(self, name):
        self.file_name = name

    def update_config(self, configs):
        self.test_config = configs

    def remove_ticket(self, test_id, board):
        if self.__is_tc_exist(test_id):
            if board in self.test_infos[test_id]['Test_Ticket']:
                del self.test_infos[test_id]['Test_Ticket'][board]

    def remove_comments(self, test_id, board):
        if self.__is_tc_exist(test_id):
            if board in self.test_infos[test_id]['Test_Comments']:
                del self.test_infos[test_id]['Test_Comments'][board]

    def remove_ltp_not_test(self):
        tc_remove_list = []
        for tc_name, tc_info in self.test_infos.items():
            if tc_info["Test_Result"] == {} and "ltp" in tc_info["Test_Path"]:
                tc_remove_list.append(tc_name)
        for tc in tc_remove_list:
            del self.test_infos[tc]

    def remove_skip_test(self):
        tc_remove_list = []
        for tc_name, tc_info in self.test_infos.items():
            if len(tc_info["Test_Result"]) == 1 and "SKIP" in tc_info["Test_Result"].values():
                tc_remove_list.append(tc_name)
        for tc in tc_remove_list:
            del self.test_infos[tc]

    def keep_pass_test(self):
        tc_remove_list = []
        for tc_name, tc_info in self.test_infos.items():
            if len(tc_info["Test_Result"]) == 0 or \
            (len(tc_info["Test_Result"]) == 1 and "PASS" not in next(iter(tc_info["Test_Result"].values()))):
                tc_remove_list.append(tc_name)
        for tc in tc_remove_list:
            del self.test_infos[tc]

    def keep_fail_test(self):
        tc_remove_list = []
        for tc_name, tc_info in self.test_infos.items():
            if len(tc_info["Test_Result"]) == 0 or \
            (len(tc_info["Test_Result"]) == 1 and "FAIL" not in next(iter(tc_info["Test_Result"].values()))):
                tc_remove_list.append(tc_name)
        for tc in tc_remove_list:
            del self.test_infos[tc]

    def change_tester(self, manual_tester="Alb-testing-team", auto_tester="jenkins"):
        for tc_info in self.test_infos.values():
            if not tc_info["Test_Tester"]:
                continue
            board_name = next(iter(tc_info["Test_Tester"]))
            if tc_info["Test_Type"].lower() == "manual":
                tc_info["Test_Tester"] = {board_name : manual_tester}
            else:
                tc_info["Test_Tester"] = {board_name : auto_tester}

    def edit_conf_tc(self):
        """ Remove tester, test type with not applicable testcase"""
        for tc_info in self.test_infos.values():
            if not tc_info["Test_Tester"]:
                continue
            if len(tc_info["Test_Result"]) != 0 and ("NOT " in next(iter(tc_info["Test_Result"].values()))
                                                     or "CONF" in next(iter(tc_info["Test_Result"].values()))):
                board_name = next(iter(tc_info["Test_Tester"]))
                tc_info["Test_Tester"] = {board_name : ""}
                tc_info["Test_Type"] = ""


class Parser(object):

    def __init__(self, data):
        self.data = data

    def parse_results(self):
        pass

    def parse_config(self, config_file):
        with open(config_file, 'r') as stream:
            nodes = re.findall(r'(\w+)=(.*?)(?:,|$)', stream.read(), flags=re.M)
        return dict(nodes)

    def parse_tc(self):
        """
        Parsing test case information from docsting
        """
        pass

    def parse_all(self):
        pass


class LtpParser(Parser):
    LTP_URL = "http://10.207.215.24/git/scm/alb/alb-ltp-tests.git"
    LTP_BRANCH = "develop"
    LTP_DIR = "/app/.gitclone/ltp" if not settings.LTP_DIR else settings.LTP_DIR

    test_os = 'linux'
    module = "ltp"

    def __init__(self, data, logger=None):
        Parser.__init__(self, data)
        if not logger:
            self.logger = utils.setup_logger(f'LtpParser', 'LtpParser.txt')
        else:
            self.logger = logger

    def parse_results(self, results_dir, board):
        self.logger.info("LTP parse result")
        for root, _dirs, _files in os.walk(results_dir):
            for _file in _files:
                # Parse Results
                if _file.endswith('.log'):
                    test_result = open(os.path.join(root, _file), encoding="utf-8", errors='ignore').read()
                    line_matcheds = re.findall(r'^([\w-]+).*?(PASS|CONF|FAIL)(?:\s+\d+.*?)?$', test_result, flags=re.I|re.M)
                    results_dict = dict(line_matcheds)
                    for tc_id in results_dict:
                        self.logger.info(f"Found result {tc_id} - {results_dict[tc_id]}")
                        self.data.new_tc_id(tc_id)
                        self.data.update_result(tc_id, {board:results_dict[tc_id]})

    def parse_tc(self):
        """
        Parsing test case information from ltp
        """
        self.logger.info(f"LTP parse testcase")
        self.logger.info("cloning")
        if os.path.exists(self.LTP_DIR):
            shutil.rmtree(self.LTP_DIR, ignore_errors=True)
        utils.clone_repo(self.LTP_URL, self.LTP_DIR, self.LTP_BRANCH)
        self.logger.info("cloned")

        ltp_testsuites = []
        # Parse Test Case ID From test suites
        # Get all nxp test suites in ltp
        for root, dirnames, files in os.walk(self.LTP_DIR):
            for _file in files:
                test_dir = os.path.join(root, _file)
                if "/.git/" not in test_dir:
                    ltp_testsuites.append(test_dir)

        for ts in ltp_testsuites:
            with open(ts, "rb") as ts_fd:
                test_suite_content = ts_fd.read().decode("utf-8")
            cases = re.findall(r'^([\w-]+)( |\t)+(.*)$', test_suite_content, flags=re.I | re.M)
            ltp_test_desc = dict([(key, value) for key, rev1, value in cases])
            for _tc in ltp_test_desc.keys():
                test_type = "Automated"
                test_desc = ltp_test_desc[_tc]
                test_path = "{slash}{module}{slash}".format(slash=os.sep, module=LtpParser.module)
                test_reqs = []
                test_platform = []
                test_suite = os.path.basename(ts).replace("nxp_", "")

                __tc = _tc
                if _tc.lower() in self.data.test_infos:
                    __tc = _tc.lower()
                elif _tc.upper() in self.data.test_infos:
                    __tc = _tc.upper()

                self.data.new_tc_id(__tc)
                self.data.update_desc(__tc, test_desc)
                self.data.update_type(__tc, test_type)
                self.data.update_path(__tc, test_path)
                self.data.update_requirements(__tc, test_reqs)
                self.data.update_platforms(__tc, test_platform)
                self.data.update_os(__tc, LtpParser.test_os)
                self.data.update_suite(__tc, test_suite)

class PytestParser(Parser):
    def __init__(self, data, logger=None):
        super().__init__(data)
        if not logger:
            self.logger = utils.setup_logger(f'PytestParser', 'PytestParser.txt')
        else:
            self.logger = logger

    def parse_results(self, result_file, board):
        self.logger.info("Pytest parse result")
        tree = et.parse(result_file)
        root = tree.getroot()
        # Loop through all testcases
        for testcase in root.iter('testcase'):
            tc_name = testcase.attrib.get('name')
            classname = testcase.attrib.get('classname')
            
            # Determine result: If 'failure', 'error', or 'skipped' tag exists, set accordingly
            if testcase.find('failure') is not None:
                tc_res = 'Fail'
            elif testcase.find('error') is not None:
                tc_res = 'Error'
            elif testcase.find('skipped') is not None:
                tc_res = 'Skip'
            else:
                tc_res = 'Pass'
            
            self.logger.info(f"Testcase: {tc_name}, Classname: {classname}, Result: {tc_res}")
            self.data.new_tc_id(tc_name)
            self.data.update_result(tc_name, {board:tc_res})
