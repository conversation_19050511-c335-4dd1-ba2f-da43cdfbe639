import pexpect
import os
import subprocess
import unicodedata
import re
import logging

from django.conf import settings

LOG_DIR = os.environ.get(
    'LOG_DIR', '/app/log') if not settings.LOG_DIR else settings.LOG_DIR
TMP_DIR = os.environ.get(
    'TMP_DIR', '/app/tmp') if not settings.TMP_DIR else settings.TMP_DIR
DATA_DIR = os.environ.get(
    'DATA_DIR', '/app/data') if not settings.DATA_DIR else settings.DATA_DIR
ALLURE_DIR = os.path.join(
    'ALLURE_DIR', "allure") if not settings.ALLURE_DIR else settings.ALLURE_DIR

formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)
if not os.path.exists(TMP_DIR):
    os.makedirs(TMP_DIR)
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)


def run_cmd(cmd, timeout=10, cwd=None):

    p = subprocess.Popen(cmd.split(' '), stdout=subprocess.PIPE, cwd=cwd)
    try:
        p.wait(timeout)
    except subprocess.TimeoutExpired:
        p.kill()
    try:
        output = p.communicate()[0].decode('utf-8')
    except:
        output = ''

    if p.returncode != 0:
        raise ValueError(
            "ERROR running '{0}'\nOutput:{1}\n\n".format(cmd, output))
    return output


def run_cmd_expect(cmd, throw=True, env=None, timeout=30, withoutput=False):
    """Run a bash command"""
    if env is not None:
        (output, exitstatus) = pexpect.run(
            cmd, env=env, withexitstatus=True, timeout=timeout)
    else:
        (output, exitstatus) = pexpect.run(
            cmd, withexitstatus=True, timeout=timeout)

    if exitstatus != 0:
        if throw:
            raise ValueError(
                "ERROR running '{0}'\nOutput:\n\n{1}".format(cmd, output))
        else:
            ret = 0
    else:
        ret = 1
    if withoutput:
        return (output, exitstatus)
    else:
        return ret


def touch(path):
    with open(path, 'a'):
        os.utime(path, None)


def slugify(value, allow_unicode=False):
    """
    Taken from https://github.com/django/django/blob/master/django/utils/text.py
    Convert to ASCII if 'allow_unicode' is False. Convert spaces or repeated
    dashes to single dashes. Remove characters that aren't alphanumerics,
    underscores, or hyphens. Convert to lowercase. Also strip leading and
    trailing whitespace, dashes, and underscores.
    """
    value = str(value)
    if allow_unicode:
        value = unicodedata.normalize('NFKC', value)
    else:
        value = unicodedata.normalize('NFKD', value).encode(
            'ascii', 'ignore').decode('ascii')
    value = re.sub(r'[^\w\s-]', '', value.lower())
    return re.sub(r'[-\s]+', '-', value).strip('-_')


"""
Method to support parsing and update variable to configuration data which is in string form
"""


def get_vars_from_configuration(config_data):
    # Parsing Release Info
    configs = {}
    try:
        # re.search('^(.*(?<!=))=(.*)', config_data, re.M)
        for data in config_data.splitlines():
            if '=' in data:
                configs[data.split('=', 1)[0]] = data.split('=', 1)[1]
        return configs
    except:
        return {}


def update_var_to_configuration(var_name, value, config):
    if re.search(f'{var_name}=.*', config):
        config = re.sub(f'{var_name}=.*', f'{var_name}={value}', config, re.M)
    else:
        config += f'\n{var_name}={value}'
    config = re.sub('[\n\r]{2,}', '\n', config)
    return config


def clone_repo(repo, dest_dir, branch="develop", submodules=False, timeout=60):
    """
    Clone repo into dest directory.
    :param repo: repo to clone
    :type repo: str
    :param dest: destination directory
    :type dest_dir: str
    :param submodules: enable/disable git submodule
    :type submodules: bool
    :param timeout: timeout clone process
    :type timeout: int
    """
    clone_cmd = "git clone --depth 1"
    if submodules:
        clone_cmd += " --recurse-submodules"
    clone_cmd += " {0} -b {1} {2}".format(repo, branch, dest_dir)
    run_cmd(clone_cmd, timeout=timeout)


def setup_logger(name, log_file, level=logging.DEBUG, reset_logfile=False):
    """To setup as many loggers"""
    log_file = os.path.join(LOG_DIR, log_file)
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    if reset_logfile and os.path.exists(log_file):
        os.remove(log_file)

    handler = logging.FileHandler(log_file)
    handler.setFormatter(formatter)

    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger.addHandler(handler)

    return logger
