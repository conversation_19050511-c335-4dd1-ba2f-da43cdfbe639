import time, os

from threading import Thread
from traceback import format_exc
from queue import Queue
from common import utils


class Worker(Thread):

    def __init__(self, Queue, fuction, logger):
        Thread.__init__(self)
        self.queue = Queue
        self.function = fuction
        self.logger = logger

    def run(self):
        while True:
            # Get the work from the queue and expand the tuple
            info = self.queue.get()
            try:
                self.logger.debug(f"QUEUE RUN {self.function.__name__} {info}")
                self.function(info)
            except Exception as e:
                self.logger.debug(f"QUEUE ERROR: {e}")
                self.logger.debug(format_exc())
            finally:
                self.logger.debug(f"QUEUE DONE {self.function.__name__} {info}")
                self.queue.task_done()


class Service(Thread):
    def __init__(self, function, interval):
        Thread.__init__(self)
        self.function = function
        self.interval = interval

    def run(self):
        while True:
            self.function()
            time.sleep(self.interval)


class WorkerManager(object):

    def __init__(self):
        self.Queue = {}
        self.Workers = []
        self.Services = []
        self.logger = utils.setup_logger(f'WorkerManager', f"WorkerManager.log", reset_logfile=True)

    def register_task(self, task_name, function, allocate_worker=3):
        if task_name not in self.Queue:
            self.Queue[task_name] = Queue()
            for x in range(allocate_worker):
                worker = Worker(self.Queue[task_name], function, self.logger)
                # Setting daemon to True will let the main thread exit even though the workers are blocking
                worker.daemon = True
                worker.start()
                self.Workers.append(worker)

    def add_to_queue(self, task_name, info):
        if task_name in self.Queue:
            queue = self.Queue[task_name]
            if info not in queue.queue:
                self.logger.debug(f"QUEUE ADDED {task_name} {info}")
                queue.put(info)

    def register_service(self, service_name, function, interval=60):
        if service_name not in self.Services:
            service = Service(function=function, interval=interval)
            service.daemon = True
            service.start()
            self.Services.append(service_name)

# Initiate worker
SYNC_TEST_RESULT_TASK = 'BUILD_CFG_ANALYZE_TASK'

WorkerManagerInstance = WorkerManager()


