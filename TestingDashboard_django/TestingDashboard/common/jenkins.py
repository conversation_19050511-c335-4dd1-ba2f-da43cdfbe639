"""Module containing Jenkins related helper functions"""

import os
import requests


class JenkinsAPI:
    """
    Helper class used for interacting with <PERSON><PERSON> via its Remote Access API
    """

    SERVER_URL = os.environ.get('JENKINS_URL', 'http://10.207.218.162:9999')
    JOB_URL = f"{SERVER_URL}/job/{{job_name}}"
    JOB_BUILD_URL = f"{SERVER_URL}/job/{{job_name}}/{{build_id}}"
    TEST_JOB_NAME = "Test"

    SUCCESS = 'SUCCESS'
    FAILURE = 'FAILURE'
    UNSTABLE = 'UNSTABLE'
    ABORTED = 'ABORTED'
    NOT_BUILT = 'NOT_BUILT'

    @staticmethod
    def get_artifact(save_path, build_id, job_name, artifact_path):
        """
        :returns: Boolean
        """
        if os.path.isfile(save_path):
            return True
        url = JenkinsAPI.JOB_BUILD_URL.format(job_name=job_name, build_id=build_id) + f"/artifact/{artifact_path}"
        print(f"url {url}")
        response = requests.get(url)
        if response.status_code == 200:
            with open(save_path, "wb") as file:
                file.write(response.content)
            return True

        print("ERROR: HTTP request failed with status code: {0} for url: {1}".format(response.status_code, url))
        return False

    @staticmethod
    def get_pytest_result(save_path, build_id, artifact_path="report.xml"):
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        return JenkinsAPI.get_artifact(save_path, build_id, JenkinsAPI.TEST_JOB_NAME, artifact_path)
    
    @staticmethod
    def get_ltp_result(save_path, build_id, artifact_path="ltp_result.zip"):
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        return JenkinsAPI.get_artifact(save_path, build_id, JenkinsAPI.TEST_JOB_NAME, artifact_path)
    
    @staticmethod
    def get_allure_result(save_path, build_id, artifact_path="allure-results.zip"):
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        return JenkinsAPI.get_artifact(save_path, build_id, JenkinsAPI.TEST_JOB_NAME, artifact_path)
