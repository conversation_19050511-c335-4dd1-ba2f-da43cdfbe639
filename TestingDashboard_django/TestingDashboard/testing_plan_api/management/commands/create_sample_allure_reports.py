from django.core.management.base import BaseCommand
from testing_plan_api.models import TestingPlanTotal, AllureReport
from datetime import datetime, timedelta
import random


class Command(BaseCommand):
    help = 'Create sample AllureReport data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=15,
            help='Number of sample allure reports to create (default: 15)'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        # Get existing TestingPlanTotal objects
        testing_plan_totals = list(TestingPlanTotal.objects.all())
        
        if not testing_plan_totals:
            self.stdout.write(
                self.style.ERROR('No TestingPlanTotal objects found. Please create some first.')
            )
            return

        # Sample report file URLs (you can replace with actual URLs)
        sample_report_urls = [
            'https://demo.qameta.io/allure/index.html',
            'https://allurereport.org/docs/gettingstarted-installation/',
            '/allure-report/index.html',  # Local report
            'https://example.com/allure-report-1/index.html',
            'https://example.com/allure-report-2/index.html',
        ]

        created_reports = []
        
        for i in range(count):
            # Random testing plan total
            testing_plan_total = random.choice(testing_plan_totals)
            
            # Random report file URL
            report_file = random.choice(sample_report_urls)
            
            # Create AllureReport
            allure_report = AllureReport.objects.create(
                testing_plan_total=testing_plan_total,
                report_file=report_file
            )
            
            # Randomize created_at to simulate different creation times
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            
            random_created_at = datetime.now() - timedelta(
                days=days_ago, 
                hours=hours_ago, 
                minutes=minutes_ago
            )
            
            # Update created_at manually
            AllureReport.objects.filter(id=allure_report.id).update(
                created_at=random_created_at
            )
            
            created_reports.append(allure_report)
            
            self.stdout.write(
                f'Created AllureReport {i+1}/{count}: '
                f'{testing_plan_total.name} - {report_file}'
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {len(created_reports)} sample AllureReport objects'
            )
        )
        
        # Show recent reports
        recent_reports = AllureReport.objects.order_by('-created_at')[:5]
        self.stdout.write('\nMost recent reports:')
        for report in recent_reports:
            self.stdout.write(
                f'  - {report.testing_plan_total.name} '
                f'({report.created_at.strftime("%Y-%m-%d %H:%M")})'
            )
