from rest_framework import serializers
from .models import TestingPlan, TestingPlanBatch, TestingPlanTotal, TestingPlanRun, Result, TestingPlanBuild, AllureReport
from config_api.serializers import BuildConfigurationSerializer, BoardConfigurationSerializer
from board_api.serializers import BoardInformationSerializer
from testcase_api.serializers import TestCaseSerializer


class TestingPlanBatchSerializer(serializers.ModelSerializer):
    test_cases_detail = TestCaseSerializer(
        source='test_cases', many=True, read_only=True)
    test_case_count = serializers.SerializerMethodField()

    class Meta:
        model = TestingPlanBatch
        fields = [
            'id', 'name', 'status', 'test_cases',
            'test_cases_detail', 'test_case_count', 'selected_modules', 'selected_suites',
            'order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_test_case_count(self, obj):
        return obj.test_cases.count()


class TestingPlanBuildSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestingPlanBuild
        fields = '__all__'


class TestingPlanSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source='created_by.username', read_only=True)
    # build_configurations_detail = BuildConfigurationSerializer(
    #     source='build_configurations', many=True, read_only=True)
    board_information_detail = BoardInformationSerializer(
        source='board_information', read_only=True)
    test_cases_detail = TestCaseSerializer(
        source='test_cases', many=True, read_only=True)
    batches_detail = TestingPlanBatchSerializer(
        source='batches', many=True, read_only=True)
    total_test_cases = serializers.SerializerMethodField()
    # estimated_duration = serializers.SerializerMethodField()
    build_jobs = TestingPlanBuildSerializer(many=True, read_only=True)

    class Meta:
        model = TestingPlan
        fields = [
            'id', 'name', 'description', 'priority', 'status',
            # 'build_configurations', 'build_configurations_detail',
            'board_information', 'board_information_detail',
            'test_cases', 'test_cases_detail', 'batches_detail',
            # 'jenkins_job_name', 'jenkins_build_number', 'jenkins_status', 'jenkins_url',
            # 'scheduled_at', 'started_at', 'completed_at',
            'total_test_cases', 'build_jobs',
            # 'estimated_duration',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_total_test_cases(self, obj):
        return obj.get_total_test_cases()

    def get_estimated_duration(self, obj):
        return obj.get_estimated_duration()

    def validate_name(self, value):
        """Validate name uniqueness"""
        if self.instance and self.instance.name == value:
            return value
        if TestingPlan.objects.filter(name=value).exists():
            raise serializers.ValidationError(
                "Testing plan name already exists")
        return value


class TestingPlanCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestingPlan
        fields = [
            'id', 'name', 'description', 'priority', 'status',
            'board_information', 'test_cases', 'testing_plan_total'
        ]
        read_only_fields = ['id']

    def validate_test_cases(self, value):
        """Ensure test_cases is always a list"""
        if not isinstance(value, list):
            return [value] if value is not None else []
        return value

    def create(self, validated_data):
        # Extract ManyToMany fields for test_cases
        test_cases_data = validated_data.pop('test_cases', [])

        # Set created_by
        validated_data['created_by'] = self.context['request'].user

        # Create the testing plan (board_information is now ForeignKey, so it's handled automatically)
        testing_plan = TestingPlan.objects.create(**validated_data)

        # Set ManyToMany relationship for test_cases
        if test_cases_data:
            testing_plan.test_cases.set(test_cases_data)

        return testing_plan


class TestingPlanBatchCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestingPlanBatch
        fields = ['name', 'status', 'test_cases',
                  'selected_modules', 'selected_suites', 'order']

    def validate_test_cases(self, value):
        """Ensure test_cases is always a list"""
        if not isinstance(value, list):
            return [value] if value is not None else []
        return value

    def validate_selected_modules(self, value):
        """Ensure selected_modules is always a list"""
        if not isinstance(value, list):
            return [value] if value is not None else []
        return value

    def validate_selected_suites(self, value):
        """Ensure selected_suites is always a list"""
        if not isinstance(value, list):
            return [value] if value is not None else []
        return value

    def create(self, validated_data):
        # Extract ManyToMany fields for test_cases
        test_cases_data = validated_data.pop('test_cases', [])

        # Create the testing plan batch (testing_plan will be set in the view)
        testing_plan_batch = TestingPlanBatch.objects.create(**validated_data)

        # Set ManyToMany relationship for test_cases
        if test_cases_data:
            testing_plan_batch.test_cases.set(test_cases_data)

        return testing_plan_batch

    def update(self, instance, validated_data):
        # Extract ManyToMany fields for test_cases
        test_cases_data = validated_data.pop('test_cases', None)

        # Update regular fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update ManyToMany relationship for test_cases
        if test_cases_data is not None:
            instance.test_cases.set(test_cases_data)

        return instance


class TestingPlanUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestingPlan
        fields = [
            'name', 'description', 'priority', 'status',
            'board_information', 'test_cases',
        ]


# TestingPlanTotal Serializers
class TestingPlanTotalSerializer(serializers.ModelSerializer):
    testing_plans = serializers.SerializerMethodField()
    total_testing_plans = serializers.SerializerMethodField()
    progress_summary = serializers.SerializerMethodField()
    created_by_username = serializers.CharField(
        source='created_by.username', read_only=True)

    class Meta:
        model = TestingPlanTotal
        fields = '__all__'

    def get_testing_plans(self, obj):
        testing_plans = obj.testing_plans.all()
        return TestingPlanSerializer(testing_plans, many=True).data

    def get_total_testing_plans(self, obj):
        return obj.testing_plans.count()

    def get_progress_summary(self, obj):
        testing_plans = obj.testing_plans.all()
        total = testing_plans.count()
        completed = testing_plans.filter(status='Completed').count()
        running = testing_plans.filter(status='Running').count()
        failed = testing_plans.filter(status='Failed').count()

        return {
            'total': total,
            'completed': completed,
            'running': running,
            'failed': failed,
            'pending': total - completed - running - failed
        }


class TestingPlanTotalCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestingPlanTotal
        fields = [
            'id', 'name', 'type', 'description', 'common', 'information',
            'date_from', 'date_to'
        ]
        read_only_fields = ['id']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


# TestingPlanRun Serializers
class TestingPlanRunSerializer(serializers.ModelSerializer):
    results = serializers.SerializerMethodField()
    results_summary = serializers.SerializerMethodField()

    class Meta:
        model = TestingPlanRun
        fields = '__all__'

    def get_results(self, obj):
        results = obj.results.all()
        return ResultSerializer(results, many=True).data

    def get_results_summary(self, obj):
        results = obj.results.all()
        total = results.count()
        passed = results.filter(result='Pass').count()
        failed = results.filter(result='Fail').count()
        skipped = results.filter(result='Skip').count()

        return {
            'total': total,
            'passed': passed,
            'failed': failed,
            'skipped': skipped
        }


class TestingPlanRunCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestingPlanRun
        fields = [
            'testing_plan_batch', 'status', 'jenkins_job_name',
            'jenkins_build_number', 'jenkins_status', 'jenkins_url',
            'scheduled_at', 'started_at', 'completed_at'
        ]


# Result Serializers
class ResultSerializer(serializers.ModelSerializer):
    test_case_detail = TestCaseSerializer(source='test_case', read_only=True)

    class Meta:
        model = Result
        fields = '__all__'


class ResultCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Result
        fields = '__all__'


# AllureReport Serializers
class AllureReportSerializer(serializers.ModelSerializer):
    testing_plan_total_name = serializers.CharField(
        source='testing_plan_total.name', read_only=True)
    testing_plan_total_type = serializers.CharField(
        source='testing_plan_total.type', read_only=True)

    class Meta:
        model = AllureReport
        fields = [
            'id', 'testing_plan_total', 'testing_plan_total_name', 'testing_plan_total_type',
            'report_file', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AllureReportCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = AllureReport
        fields = ['testing_plan_total', 'report_file']
