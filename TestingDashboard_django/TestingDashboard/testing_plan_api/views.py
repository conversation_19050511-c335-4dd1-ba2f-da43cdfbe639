import os
import json
from datetime import datetime
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from django.db import models
from django.shortcuts import get_object_or_404
from common import utils
from common.<PERSON><PERSON><PERSON> import JenkinsAP<PERSON>
from common.tc_result import TestcaseResult
from common.workers_manager import WorkerManagerInstance, SYNC_TEST_RESULT_TASK
from .models import TestingPlan, TestingPlanBatch, TestingPlanTotal, TestingPlanRun, Result, TestingPlanBuild, AllureReport
from .serializers import (
    TestingPlanSerializer, TestingPlanCreateSerializer, TestingPlanUpdateSerializer,
    TestingPlanBatchSerializer, TestingPlanBatchCreateSerializer,
    TestingPlanTotalSerializer, TestingPlanTotalCreateSerializer,
    TestingPlanRunSerializer, TestingPlanRunCreateSerializer,
    ResultSerializer, ResultCreateSerializer,
    AllureReportSerializer, AllureReportCreateSerializer
)

from rest_framework.permissions import AllowAny
from rest_framework.pagination import PageNumberPagination
from django.views.decorators.csrf import csrf_exempt


class OptionalPagination(PageNumberPagination):
    page_size = 20

    def paginate_queryset(self, queryset, request, view=None):
        if request.query_params.get('all') == 'true':
            return None
        return super().paginate_queryset(queryset, request, view)


# Testing Plan Views
WorkerManagerInstance.register_task(
    SYNC_TEST_RESULT_TASK, TestcaseResult.sync_test_result)


class TestingPlanListCreateView(generics.ListCreateAPIView):
    queryset = TestingPlan.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TestingPlanCreateSerializer
        return TestingPlanSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            print("Validation errors:", serializer.errors)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            self.perform_create(serializer)
            print("Testing plan created successfully")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(f"Error creating testing plan: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def get_queryset(self):
        queryset = TestingPlan.objects.all()
        tplan_total_filter = self.request.query_params.get(
            'testing_plan_total', None)
        if tplan_total_filter:
            queryset = queryset.filter(testing_plan_total=tplan_total_filter)

        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        priority_filter = self.request.query_params.get('priority', None)
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('-created_at')


class TestingPlanDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = TestingPlan.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return TestingPlanUpdateSerializer
        return TestingPlanSerializer

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete testing plans'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)

# Testing Plan Batch Views


class TestingPlanBatchListCreateView(generics.ListCreateAPIView):
    serializer_class = TestingPlanBatchSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        testing_plan_id = self.kwargs['testing_plan_id']
        return TestingPlanBatch.objects.filter(testing_plan_id=testing_plan_id)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TestingPlanBatchCreateSerializer
        return TestingPlanBatchSerializer

    def perform_create(self, serializer):
        testing_plan_id = self.kwargs['testing_plan_id']
        testing_plan = get_object_or_404(TestingPlan, id=testing_plan_id)
        serializer.save(testing_plan=testing_plan)


class TestingPlanBatchDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = TestingPlanBatchSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        testing_plan_id = self.kwargs['testing_plan_id']
        return TestingPlanBatch.objects.filter(testing_plan_id=testing_plan_id)

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return TestingPlanBatchCreateSerializer
        return TestingPlanBatchSerializer

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete testing plan batches'},
                status=status.HTTP_403_FORBIDDEN
            )
        super().delete(request, *args, **kwargs)
        return Response(
            {'message': 'Testing plan batch deleted successfully'},
            status=status.HTTP_200_OK
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def trigger_jenkins_build(request, pk):
    """Trigger Jenkins build for a testing plan"""
    testing_plan = get_object_or_404(TestingPlan, pk=pk)

    # Only admin and engineer can trigger builds
    if request.user.profile.role not in ['admin', 'engineer']:
        return Response(
            {'error': 'Only admin and engineer users can trigger Jenkins builds'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Update testing plan status
    testing_plan.status = 'Running'
    testing_plan.jenkins_job_name = request.data.get('job_name', 'default_job')
    testing_plan.save()

    # Here you would integrate with Jenkins API
    # For now, return a mock response
    return Response({
        'message': 'Jenkins build triggered successfully',
        'testing_plan_id': testing_plan.id,
        'jenkins_job_name': testing_plan.jenkins_job_name,
        'status': testing_plan.status
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def testing_plan_choices(request):
    """Get choices for testing plan fields"""
    choices = {
        'status_choices': TestingPlan.STATUS_CHOICES,
        'batch_status_choices': TestingPlanBatch.STATUS_CHOICES,
        'testing_plan_total_type_choices': TestingPlanTotal.TESTING_PLAN_TYPE_CHOICES,
        'run_status_choices': TestingPlanRun.STATUS_CHOICES,
        'result_choices': Result.TESING_RESULT_CHOICES,
    }
    return Response(choices)


# TestingPlanTotal Views
class TestingPlanTotalListCreateView(generics.ListCreateAPIView):
    queryset = TestingPlanTotal.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            try:
                return TestingPlanTotalCreateSerializer
            except Exception as e:
                print(f"Error creating TestingPlanTotal: {e}")
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        try:
            return TestingPlanTotalSerializer
        except Exception as e:
            print(f"Error getting TestingPlanTotal: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def get_queryset(self):
        queryset = TestingPlanTotal.objects.all()
        type_filter = self.request.query_params.get('type', None)
        if type_filter:
            queryset = queryset.filter(type=type_filter)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('-created_at')


class TestingPlanTotalDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = TestingPlanTotal.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return TestingPlanTotalCreateSerializer
        return TestingPlanTotalSerializer

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete testing plan totals'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)


# TestingPlanRun Views
class TestingPlanRunListCreateView(generics.ListCreateAPIView):
    serializer_class = TestingPlanRunSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        batch_id = self.kwargs['batch_id']
        return TestingPlanRun.objects.filter(testing_plan_batch_id=batch_id)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TestingPlanRunCreateSerializer
        return TestingPlanRunSerializer

    def perform_create(self, serializer):
        batch_id = self.kwargs['batch_id']
        batch = get_object_or_404(TestingPlanBatch, id=batch_id)
        serializer.save(testing_plan_batch=batch)


class TestingPlanRunDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = TestingPlanRunSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        batch_id = self.kwargs['batch_id']
        return TestingPlanRun.objects.filter(testing_plan_batch_id=batch_id)

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return TestingPlanRunCreateSerializer
        return TestingPlanRunSerializer

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete testing plan runs'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)


# Result Views
class ResultListCreateView(generics.ListCreateAPIView):
    serializer_class = ResultSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        run_id = self.kwargs['run_id']
        return Result.objects.filter(testing_plan_run_id=run_id)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ResultCreateSerializer
        return ResultSerializer

    def perform_create(self, serializer):
        run_id = self.kwargs['run_id']
        run = get_object_or_404(TestingPlanRun, id=run_id)
        serializer.save(testing_plan_run=run)


class ResultDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ResultSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        run_id = self.kwargs['run_id']
        return Result.objects.filter(testing_plan_run_id=run_id)

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ResultCreateSerializer
        return ResultSerializer

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete results'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def trigger_jenkins_build_total(request, pk):
    """Trigger Jenkins build for all testing plans in a testing plan total"""
    testing_plan_total = get_object_or_404(TestingPlanTotal, pk=pk)

    # Only admin and engineer can trigger builds
    if request.user.profile.role not in ['admin', 'engineer']:
        return Response(
            {'error': 'Only admin and engineer users can trigger Jenkins builds'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Update all testing plans status to Running
    testing_plans = testing_plan_total.testing_plans.all()
    for plan in testing_plans:
        plan.status = 'Running'
        plan.save()

    # Here you would integrate with Jenkins API
    # For now, return a mock response
    return Response({
        'message': 'Jenkins builds triggered successfully for all testing plans',
        'testing_plan_total_id': testing_plan_total.id,
        'triggered_plans': testing_plans.count()
    })


@csrf_exempt
@api_view(["POST"])
@permission_classes([AllowAny])
@authentication_classes([])
def update_jenkins_run(request):
    """Update Jenkins run"""
    testing_plan_batch = request.data.get("testing_plan_batch", None)
    jenkins_url = request.data.get("jenkins_url", None)
    status = request.data.get("status", "Pending")
    jenkins_job_name = request.data.get("jenkins_job_name", None)
    jenkins_build_number = request.data.get("jenkins_build_number", None)
    jenkins_status = request.data.get("jenkins_status", "In Queue")
    device = request.data.get("device", "")
    if testing_plan_batch:
        # Create jenkins run job
        testing_plan_batch_obj = TestingPlanBatch.objects.get(
            pk=testing_plan_batch)
        job_run_obj = TestingPlanRun.objects.create(
            testing_plan_batch=testing_plan_batch_obj,
            status=status,
            jenkins_job_name=jenkins_job_name,
            jenkins_build_number=jenkins_build_number,
            jenkins_status=jenkins_status,
            jenkins_url=jenkins_url
        )
        return Response({"success": "Job run is saved to database"})
    else:
        # Update jenkins run job
        job_run_obj = TestingPlanRun.objects.get(jenkins_url=jenkins_url)
        if not job_run_obj:
            return Response({"error": f"Run job {jenkins_url} not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            job_run_obj.status = status
            job_run_obj.jenkins_status = jenkins_status
            if status == "Completed":
                job_run_obj.completed_at = datetime.now()
            job_run_obj.save()
            if job_run_obj.jenkins_job_name == JenkinsAPI.TEST_JOB_NAME and status == "Completed" and jenkins_status != "FAILURE":
                WorkerManagerInstance.add_to_queue(
                    SYNC_TEST_RESULT_TASK, (job_run_obj.pk, device))
                return Response({"success": "Job run is updated to database and synchoronizing test result from Jenkins"})
            return Response({"success": "Job run is updated to database"})


@csrf_exempt
@api_view(["POST"])
@permission_classes([AllowAny])
@authentication_classes([])
def update_jenkins_build(request):
    """Update Jeknins build"""
    testing_plan_id = request.data.get("testing_plan_id", None)
    jenkins_url = request.data.get("jenkins_url", None)
    jenkins_job_name = request.data.get("jenkins_job_name", None)
    jenkins_build_number = request.data.get("jenkins_build_number", None)
    jenkins_status = request.data.get("jenkins_status", "In Queue")
    artifact_url = request.data.get("artifact_url", None)
    # Update jenkins build job
    if testing_plan_id:
        testing_plan_obj = TestingPlan.objects.get(pk=testing_plan_id)
        job_build_obj = TestingPlanBuild.objects.create(
            testing_plan=testing_plan_obj,
            jenkins_job_name=jenkins_job_name,
            jenkins_build_number=jenkins_build_number,
            jenkins_status=jenkins_status,
            jenkins_url=jenkins_url,
        )
        return Response({"success": "Job build is created to database"})
    else:
        job_build_obj = TestingPlanBuild.objects.get(jenkins_url=jenkins_url)
        if not job_build_obj:
            return Response({"error": f"Build job {jenkins_url} not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            job_build_obj.jenkins_status = jenkins_status
            artifact_url = artifact_url
            if jenkins_status in (JenkinsAPI.SUCCESS, JenkinsAPI.FAILURE, JenkinsAPI.ABORTED, JenkinsAPI.NOT_BUILT, JenkinsAPI.UNSTABLE):
                job_build_obj.completed_at = datetime.now()
            job_build_obj.save()
            return Response({"success": "Job build is updated to database"})


@csrf_exempt
@api_view(["GET"])
@permission_classes([AllowAny])
@authentication_classes([])
def get_tplan_batch_data(request):
    """Return tplan batch data for Jenkins run job"""
    tplan_id = request.data.get("testing_plan_id", None)
    testing_plan_batch_obj = TestingPlan.objects.get(pk=tplan_id)
    if not testing_plan_batch_obj:
        return Response({"fail": "Can't find testing plan id {testing_plan}"})

    batch_data = {}
    for batch_obj in testing_plan_batch_obj.batches.all():
        batch_name = f"{batch_obj.pk} - {testing_plan_batch_obj.name} - {batch_obj.name}"
        tests = {
            "ltp": {
            },
            "linux": {
            },
            "bootloader": {
            }
        }
        for tc_obj in batch_obj.test_cases.all():
            if "ltp" in tc_obj.module:
                if tc_obj.suite not in tests['ltp']:
                    tests['ltp'][tc_obj.suite] = []
                tests['ltp'][tc_obj.suite].append(tc_obj.testcase_id)
        batch_data[batch_name] = {
            "Id": str(batch_obj.pk), "Tests": json.dumps(tests, indent=2)}

    return Response(batch_data)


# AllureReport Views
class AllureReportListCreateView(generics.ListCreateAPIView):
    queryset = AllureReport.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = OptionalPagination

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return AllureReportCreateSerializer
        return AllureReportSerializer

    def get_queryset(self):
        queryset = AllureReport.objects.select_related('testing_plan_total')

        # Filter by testing_plan_total if provided
        testing_plan_total_id = self.request.query_params.get(
            'testing_plan_total', None)
        if testing_plan_total_id:
            queryset = queryset.filter(
                testing_plan_total_id=testing_plan_total_id)

        # Handle ordering parameter
        ordering = self.request.query_params.get('ordering', '-created_at')
        if ordering:
            # Validate ordering field to prevent injection
            valid_orderings = ['created_at', '-created_at',
                               'testing_plan_total__name', '-testing_plan_total__name']
            if ordering in valid_orderings:
                queryset = queryset.order_by(ordering)
            else:
                queryset = queryset.order_by('-created_at')
        else:
            queryset = queryset.order_by('-created_at')

        return queryset


class AllureReportDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = AllureReport.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return AllureReportCreateSerializer
        return AllureReportSerializer

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete allure reports'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def recent_allure_reports(request):
    """Get 10 most recent allure reports for sidebar"""
    reports = AllureReport.objects.select_related(
        'testing_plan_total').order_by('-created_at')[:10]
    serializer = AllureReportSerializer(reports, many=True)
    return Response(serializer.data)
