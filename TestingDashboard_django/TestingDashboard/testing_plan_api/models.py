from django.db import models
from django.contrib.auth.models import User
from config_api.models import BuildConfiguration, BoardConfiguration, BoardInformation
from testcase_api.models import TestCase


class TestingPlanTotal(models.Model):
    TESTING_PLAN_TYPE_CHOICES = [
        ('Release', 'Release'),
        ('PR', 'PR'),
        ('Nightly', 'Nightly'),
        ('Weekly', 'Weekly'),
    ]

    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=200, unique=True)
    type = models.CharField(
        max_length=200, choices=TESTING_PLAN_TYPE_CHOICES, default='Release')
    description = models.JSONField(default=dict, blank=True)
    common = models.JSONField(default=dict, blank=True)
    information = models.TextField(blank=True)
    date_from = models.DateField()
    date_to = models.DateField()
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_testing_plan_total')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"[{self.type}] {self.name}"


class TestingPlan(models.Model):
    STATUS_CHOICES = [
        ('Draft', 'Draft'),
        ('Active', 'Active'),
        ('Running', 'Running'),
        ('Completed', 'Completed'),
        ('Failed', 'Failed'),
        ('Cancelled', 'Cancelled'),
    ]

    testing_plan_total = models.ForeignKey(
        TestingPlanTotal, on_delete=models.CASCADE, related_name='testing_plans', null=True, blank=True)
    name = models.CharField(max_length=200)
    description = models.JSONField(default=dict, blank=True)
    priority = models.IntegerField(default=100)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='Draft')
    board_information = models.ForeignKey(
        BoardInformation, on_delete=models.CASCADE, related_name='testing_plans', null=True, blank=True)
    test_cases = models.ManyToManyField(
        TestCase, blank=True, related_name='testing_plans')
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_testing_plans')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.status})"

    def get_total_test_cases(self):
        return self.test_cases.count()

    def get_estimated_duration(self):
        """Calculate estimated duration based on test cases"""
        # Return default duration since TestCase doesn't have estimated_time field
        return self.test_cases.count() * 30  # 30 minutes per test case


class TestingPlanBuild(models.Model):

    testing_plan = models.ForeignKey(
        TestingPlan, on_delete=models.CASCADE, related_name='build_jobs')
    jenkins_job_name = models.CharField(max_length=200, blank=True)
    jenkins_build_number = models.IntegerField(unique=True)
    jenkins_status = models.CharField(max_length=50, blank=True)
    jenkins_url = models.URLField(blank=True)
    artifact_url = models.URLField(blank=True)
    scheduled_at = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.testing_plan} - {self.jenkins_build_number}"


class TestingPlanBatch(models.Model):

    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Running', 'Running'),
        ('Completed', 'Completed'),
        ('Failed', 'Failed'),
        ('Skipped', 'Skipped'),
    ]

    testing_plan = models.ForeignKey(
        TestingPlan, on_delete=models.CASCADE, related_name='batches')
    name = models.CharField(max_length=200)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='Pending')
    test_cases = models.ManyToManyField(
        TestCase, related_name='batches', blank=True)
    # Store selected modules and suites for management
    selected_modules = models.JSONField(
        default=list, blank=True, help_text="List of selected module names")
    selected_suites = models.JSONField(
        default=list, blank=True, help_text="List of selected suite names")
    order = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['testing_plan', 'order', 'name']
        unique_together = ['testing_plan', 'name']

    def __str__(self):
        return f"{self.testing_plan.name} - {self.name}"


class TestingPlanRun(models.Model):

    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Running', 'Running'),
        ('Completed', 'Completed'),
        ('Failed', 'Failed'),
        ('Skipped', 'Skipped'),
    ]

    testing_plan_batch = models.ForeignKey(
        TestingPlanBatch, on_delete=models.CASCADE, related_name='runs', blank=True, null=True)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='Pending')
    jenkins_job_name = models.CharField(max_length=200, blank=True)
    jenkins_build_number = models.IntegerField(null=True, blank=True)
    jenkins_status = models.CharField(max_length=50, blank=True)
    jenkins_url = models.URLField(blank=True)
    scheduled_at = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)


class Result(models.Model):
    TESING_RESULT_CHOICES = [
        ('Pass', 'Pass'),
        ('Fail', 'Fail'),
        ('Skip', 'Skip'),
    ]

    testing_plan_run = models.ForeignKey(
        TestingPlanRun, on_delete=models.CASCADE, related_name='results', blank=True, null=True)
    test_case = models.ForeignKey(
        TestCase, on_delete=models.CASCADE, related_name='results')
    result = models.CharField(
        max_length=20, choices=TESING_RESULT_CHOICES, default='Pass')
    log = models.TextField(blank=True)
    note = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class AllureReport(models.Model):
    testing_plan_total = models.ForeignKey(
        TestingPlanTotal, on_delete=models.CASCADE, related_name='allure_reports')
    report_file = models.TextField(blank=True, default='')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.testing_plan_total.name} - {self.created_at}"

    class Meta:
        ordering = ['-created_at']
