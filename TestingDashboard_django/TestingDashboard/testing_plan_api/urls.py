from django.urls import path
from . import views

urlpatterns = [
    # Testing Plan Totals
    path('testing-plan-totals/', views.TestingPlanTotalListCreateView.as_view(),
         name='testing-plan-total-list-create'),
    path('testing-plan-totals/<int:pk>/',
         views.TestingPlanTotalDetailView.as_view(), name='testing-plan-total-detail'),
    path('testing-plan-totals/<int:pk>/trigger-jenkins/',
         views.trigger_jenkins_build_total, name='testing-plan-total-trigger-jenkins'),

    # Testing Plans
    path('testing-plans/', views.TestingPlanListCreateView.as_view(),
         name='testing-plan-list-create'),
    path('testing-plans/<int:pk>/', views.TestingPlanDetailView.as_view(),
         name='testing-plan-detail'),
    path('testing-plans/<int:pk>/trigger-jenkins/',
         views.trigger_jenkins_build, name='testing-plan-trigger-jenkins'),

    # Testing Plan Batches
    path('testing-plans/<int:testing_plan_id>/batches/',
         views.TestingPlanBatchListCreateView.as_view(), name='testing-plan-batch-list-create'),
    path('testing-plans/<int:testing_plan_id>/batches/<int:pk>/',
         views.TestingPlanBatchDetailView.as_view(), name='testing-plan-batch-detail'),
    path('get-tplan-batch-data', views.get_tplan_batch_data,
         name="get-tplan-batch-data"),

    # Testing Plan Runs
    path('batches/<int:batch_id>/runs/', views.TestingPlanRunListCreateView.as_view(),
         name='testing-plan-run-list-create'),
    path('batches/<int:batch_id>/runs/<int:pk>/',
         views.TestingPlanRunDetailView.as_view(), name='testing-plan-run-detail'),

    # Results
    path('runs/<int:run_id>/results/',
         views.ResultListCreateView.as_view(), name='result-list-create'),
    path('runs/<int:run_id>/results/<int:pk>/',
         views.ResultDetailView.as_view(), name='result-detail'),

    # Choices
    path('choices/', views.testing_plan_choices, name='testing-plan-choices'),

    # AllureReport
    path('allure-reports/', views.AllureReportListCreateView.as_view(),
         name='allure-report-list-create'),
    path('allure-reports/<int:pk>/', views.AllureReportDetailView.as_view(),
         name='allure-report-detail'),
    path('allure-reports/recent/', views.recent_allure_reports,
         name='recent-allure-reports'),

    # Update jenkins API
    path('update-jenkins-run', views.update_jenkins_run,
         name="update-jenkins-run"),
    path('update-jenkins-build', views.update_jenkins_build,
         name="update-jenkins-build"),
]
