from django.db import models
from django.contrib.auth.models import User


class BoardInformation(models.Model):
    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Inactive', 'Inactive'),
        ('Maintenance', 'Maintenance'),
    ]

    name = models.CharField(max_length=200, unique=True)
    description = models.JSONField(
        default=dict, blank=True)  # JSON description
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='Active')
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_board_info')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class BoardHW(models.Model):
    name = models.CharField(max_length=200, unique=True)  # Board HW Name
    board_information = models.ForeignKey(
        BoardInformation, on_delete=models.CASCADE, related_name='hardware')  # Board Information
    description = models.JSONField(
        default=dict, blank=True)  # Board Description (JSON format)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_board_hw')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.board_information.name})"


class BoardTopology(models.Model):
    name = models.CharField(max_length=200, unique=True)  # Topology Name
    board_information = models.ManyToManyField(
        BoardInformation, related_name='topologies', blank=True)  # Board Information (can select multiple)
    description = models.JSONField(
        default=dict, blank=True)  # Description (JSON format)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_board_topology')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        board_names = ", ".join(
            [board.name for board in self.board_information.all()[:3]])
        if self.board_information.count() > 3:
            board_names += f" (+{self.board_information.count() - 3} more)"
        return f"{self.name} ({board_names})"

    def get_board_information_names(self):
        """Get comma-separated board information names"""
        return ", ".join([board.name for board in self.board_information.all()])
