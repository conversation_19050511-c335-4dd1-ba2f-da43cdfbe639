from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db import models
from .models import BoardInformation, BoardHW, BoardTopology
from .serializers import (
    BoardInformationSerializer, BoardInformationCreateSerializer,
    BoardHWSerializer, BoardHWCreateSerializer,
    BoardTopologySerializer, BoardTopologyCreateSerializer
)

# Board Information Views


class BoardInformationListCreateView(generics.ListCreateAPIView):
    queryset = BoardInformation.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BoardInformationCreateSerializer
        return BoardInformationSerializer

    def get_queryset(self):
        queryset = BoardInformation.objects.all()
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('name')


class BoardInformationDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = BoardInformation.objects.all()
    serializer_class = BoardInformationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete board information'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)

# Board HW Views


class BoardHWListCreateView(generics.ListCreateAPIView):
    queryset = BoardHW.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BoardHWCreateSerializer
        return BoardHWSerializer

    def get_queryset(self):
        queryset = BoardHW.objects.all()
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        board_info_filter = self.request.query_params.get(
            'board_information', None)
        if board_info_filter:
            queryset = queryset.filter(board_information=board_info_filter)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('name')


class BoardHWDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = BoardHW.objects.all()
    serializer_class = BoardHWSerializer
    permission_classes = [permissions.IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete board hardware'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)

# Board Topology Views


class BoardTopologyListCreateView(generics.ListCreateAPIView):
    queryset = BoardTopology.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BoardTopologyCreateSerializer
        return BoardTopologySerializer

    def get_queryset(self):
        queryset = BoardTopology.objects.all()
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('name')


class BoardTopologyDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = BoardTopology.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return BoardTopologyCreateSerializer
        return BoardTopologySerializer

    def put(self, request, *args, **kwargs):
        try:
            return super().put(request, *args, **kwargs)
        except Exception as e:
            print(f"Error updating BoardTopology: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request, *args, **kwargs):
        if not request.user.profile.role == 'admin':
            return Response(
                {'error': 'Only admin users can delete board topology'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().delete(request, *args, **kwargs)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def board_choices(request):
    """Get choices for board fields"""
    choices = {
        'status_choices': BoardInformation.STATUS_CHOICES,
    }
    return Response(choices)
