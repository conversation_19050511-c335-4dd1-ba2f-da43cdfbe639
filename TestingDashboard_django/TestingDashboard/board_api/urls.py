from django.urls import path
from . import views

urlpatterns = [
    # Board Information
    path('board-information/', views.BoardInformationListCreateView.as_view(), name='board-information-list-create'),
    path('board-information/<int:pk>/', views.BoardInformationDetailView.as_view(), name='board-information-detail'),
    
    # Board HW
    path('board-hw/', views.BoardHWListCreateView.as_view(), name='board-hw-list-create'),
    path('board-hw/<int:pk>/', views.BoardHWDetailView.as_view(), name='board-hw-detail'),
    
    # Board Topology
    path('board-topology/', views.BoardTopologyListCreateView.as_view(), name='board-topology-list-create'),
    path('board-topology/<int:pk>/', views.BoardTopologyDetailView.as_view(), name='board-topology-detail'),
    
    # Choices
    path('choices/', views.board_choices, name='board-choices'),
]
