from django.contrib import admin
from .models import BoardInformation, BoardHW, BoardTopology


@admin.register(BoardInformation)
class BoardInformationAdmin(admin.ModelAdmin):
    list_display = ['name', 'status', 'created_by', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(BoardHW)
class BoardHWAdmin(admin.ModelAdmin):
    list_display = ['name', 'board_information',
                    'created_by', 'created_at']
    list_filter = ['board_information', 'created_at']
    search_fields = ['name', 'board_information__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(BoardTopology)
class BoardTopologyAdmin(admin.ModelAdmin):
    list_display = ['name', 'get_board_info_names', 'created_by', 'created_at']
    list_filter = ['board_information', 'created_at']
    search_fields = ['name', 'board_information__name']
    readonly_fields = ['created_at', 'updated_at']
    filter_horizontal = ['board_information']  # Nice UI for ManyToMany

    def get_board_info_names(self, obj):
        """Display board information names in admin list"""
        return obj.get_board_information_names()
    get_board_info_names.short_description = 'Board Information'
