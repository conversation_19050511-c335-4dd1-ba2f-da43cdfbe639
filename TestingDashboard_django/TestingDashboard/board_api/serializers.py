from rest_framework import serializers
from .models import BoardInformation, BoardHW, BoardTopology


class BoardInformationSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source='created_by.username', read_only=True)

    class Meta:
        model = BoardInformation
        fields = [
            'id', 'name', 'description', 'status',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name uniqueness"""
        if self.instance and self.instance.name == value:
            return value
        if BoardInformation.objects.filter(name=value).exists():
            raise serializers.ValidationError(
                "Board information name already exists")
        return value


class BoardInformationCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BoardInformation
        fields = ['name', 'description', 'status']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class BoardHWSerializer(serializers.ModelSerializer):
    board_information_name = serializers.CharField(
        source='board_information.name', read_only=True)

    class Meta:
        model = BoardHW
        fields = [
            'id', 'name', 'board_information', 'board_information_name', 'description'
        ]
        read_only_fields = ['id']

    def validate_name(self, value):
        """Validate name uniqueness"""
        if self.instance and self.instance.name == value:
            return value
        if BoardHW.objects.filter(name=value).exists():
            raise serializers.ValidationError("Board HW name already exists")
        return value


class BoardHWCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BoardHW
        fields = ['name', 'board_information', 'description']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class BoardTopologySerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source='created_by.username', read_only=True)
    board_information_names = serializers.SerializerMethodField()

    class Meta:
        model = BoardTopology
        fields = [
            'id', 'name', 'board_information', 'board_information_names',
            'description', 'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_board_information_names(self, obj):
        """Get comma-separated board information names"""
        return obj.get_board_information_names()

    def validate_name(self, value):
        """Validate name uniqueness"""
        if self.instance and self.instance.name == value:
            return value
        if BoardTopology.objects.filter(name=value).exists():
            raise serializers.ValidationError(
                "Board topology name already exists")
        return value


class BoardTopologyCreateSerializer(serializers.ModelSerializer):
    board_information = serializers.PrimaryKeyRelatedField(
        queryset=BoardInformation.objects.all(),
        many=True,
        required=False
    )

    class Meta:
        model = BoardTopology
        fields = ['name', 'board_information', 'description']

    def validate_board_information(self, value):
        """Validate board information IDs exist"""
        if not value:
            return value
        existing_ids = BoardInformation.objects.filter(
            id__in=value).values_list('id', flat=True)
        invalid_ids = set(value) - set(existing_ids)
        if invalid_ids:
            raise serializers.ValidationError(
                f"Invalid board information IDs: {list(invalid_ids)}")
        return value

    def create(self, validated_data):
        board_information_data = validated_data.pop('board_information', [])
        validated_data['created_by'] = self.context['request'].user
        topology = BoardTopology.objects.create(**validated_data)
        topology.board_information.set(board_information_data)
        return topology

    def update(self, instance, validated_data):
        board_information_data = validated_data.pop('board_information', None)

        # Update other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update board_information if provided
        if board_information_data is not None:
            instance.board_information.set(board_information_data)

        return instance
