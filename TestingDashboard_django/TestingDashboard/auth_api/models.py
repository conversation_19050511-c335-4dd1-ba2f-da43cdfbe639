from django.db import models
from django.contrib.auth.models import User

ROLE_CHOICES = (
    ('admin', 'Administrator'),
    ('engineer', 'Engineer'),
    ('viewer', 'Viewer'),
)

STATUS_CHOICES = (
    ('active', 'Active'),
    ('inactive', 'Inactive'),
)

# Create your models here.


class Profile(models.Model):

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(
        max_length=100, choices=ROLE_CHOICES, default='viewer')
    status = models.Char<PERSON>ield(
        max_length=100, choices=STATUS_CHOICES, default='active')

    def full_name(self):
        return f"{self.user.first_name} {self.user.last_name}"

    def __str__(self):
        return self.user.username
