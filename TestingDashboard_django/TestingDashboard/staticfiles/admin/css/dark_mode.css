@media (prefers-color-scheme: dark) {
    :root {
      --primary: #264b5d;
      --primary-fg: #f7f7f7;
  
      --body-fg: #eeeeee;
      --body-bg: #121212;
      --body-quiet-color: #d0d0d0;
      --body-medium-color: #e0e0e0;
      --body-loud-color: #ffffff;
  
      --breadcrumbs-link-fg: #e0e0e0;
      --breadcrumbs-bg: var(--primary);
  
      --link-fg: #81d4fa;
      --link-hover-color: #4ac1f7;
      --link-selected-fg: #6f94c6;
  
      --hairline-color: #272727;
      --border-color: #353535;
  
      --error-fg: #e35f5f;
      --message-success-bg: #006b1b;
      --message-warning-bg: #583305;
      --message-error-bg: #570808;
  
      --darkened-bg: #212121;
      --selected-bg: #1b1b1b;
      --selected-row: #00363a;
  
      --close-button-bg: #333333;
      --close-button-hover-bg: #666666;

      color-scheme: dark;
    }
  }


html[data-theme="dark"] {
    --primary: #264b5d;
    --primary-fg: #f7f7f7;

    --body-fg: #eeeeee;
    --body-bg: #121212;
    --body-quiet-color: #d0d0d0;
    --body-medium-color: #e0e0e0;
    --body-loud-color: #ffffff;

    --breadcrumbs-link-fg: #e0e0e0;
    --breadcrumbs-bg: var(--primary);

    --link-fg: #81d4fa;
    --link-hover-color: #4ac1f7;
    --link-selected-fg: #6f94c6;

    --hairline-color: #272727;
    --border-color: #353535;

    --error-fg: #e35f5f;
    --message-success-bg: #006b1b;
    --message-warning-bg: #583305;
    --message-error-bg: #570808;

    --darkened-bg: #212121;
    --selected-bg: #1b1b1b;
    --selected-row: #00363a;

    --close-button-bg: #333333;
    --close-button-hover-bg: #666666;

    color-scheme: dark;
}

/* THEME SWITCH */
.theme-toggle {
    cursor: pointer;
    border: none;
    padding: 0;
    background: transparent;
    vertical-align: middle;
    margin-inline-start: 5px;
    margin-top: -1px;
}

.theme-toggle svg {
    vertical-align: middle;
    height: 1.5rem;
    width: 1.5rem;
    display: none;
}

/*
Fully hide screen reader text so we only show the one matching the current
theme.
*/
.theme-toggle .visually-hidden {
    display: none;
}

html[data-theme="auto"] .theme-toggle .theme-label-when-auto {
    display: block;
}

html[data-theme="dark"] .theme-toggle .theme-label-when-dark {
    display: block;
}

html[data-theme="light"] .theme-toggle .theme-label-when-light {
    display: block;
}

/* ICONS */
.theme-toggle svg.theme-icon-when-auto,
.theme-toggle svg.theme-icon-when-dark,
.theme-toggle svg.theme-icon-when-light {
    fill: var(--header-link-color);
    color: var(--header-bg);
}

html[data-theme="auto"] .theme-toggle svg.theme-icon-when-auto {
    display: block;
}

html[data-theme="dark"] .theme-toggle svg.theme-icon-when-dark {
    display: block;
}

html[data-theme="light"] .theme-toggle svg.theme-icon-when-light {
    display: block;
}
