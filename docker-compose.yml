services:
  backend:
    build: ./TestingDashboard_django
    container_name: django_backend
    ports:
      - "8000:8000"
    volumes:
      - ./TestingDashboard_django:/app
    working_dir: /app/TestingDashboard
    command: >
      sh -c "python manage.py makemigrations &&
             python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"
    environment:
      - DEBUG=True
      - JENKINS_URL=http://**************:9999
      - LOG_DIR=/app/log
      - TMP_DIR=/app/tmp
      - DATA_DIR=/app/data
    networks:
      - app_net

  frontend:
    build: ./TestingDashboard
    container_name: react_frontend
    ports:
      - "9010:5173"
    volumes:
      # - ./TestingDashboard:/app
      - ./TestingDashboard/public:/app/public
      - ./TestingDashboard/src:/app/src
    working_dir: /app
    command: npm run dev -- --host
    environment:
      # - VITE_API_URL=http://*************:8000
      # - VITE_API_BASE_URL=http://*************:8000
      - VITE_API_URL=http://**************:8000
      - VITE_API_BASE_URL=http://**************:8000
      - VITE_JENKINS_URL=http://**************:9999
    networks:
      - app_net

networks:
  app_net:
    driver: bridge
