import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { useLocalStorage } from '../hooks/useLocalStorage';

const BoardSetup = () => {
  const [boards, setBoards] = useLocalStorage('boards', [
    {
      id: 1,
      name: 'Raspberry Pi 4',
      configuration: JSON.stringify({
        cpu: 'ARM Cortex-A72',
        memory: '4GB LPDDR4',
        storage: '32GB microSD',
        network: 'Gigabit Ethernet, WiFi 802.11ac',
        gpio: '40-pin GPIO header',
      }, null, 2),
    },
    {
      id: 2,
      name: 'BeagleBone Black',
      configuration: JSON.stringify({
        cpu: 'ARM Cortex-A8',
        memory: '512MB DDR3',
        storage: '4GB eMMC',
        network: '10/100 Ethernet',
        gpio: '92 GPIO pins',
      }, null, 2),
    },
    {
      id: 3,
      name: 'Intel NUC',
      configuration: JSON.stringify({
        cpu: 'Intel Core i5',
        memory: '16GB DDR4',
        storage: '256GB NVMe SSD',
        network: 'Gigabit Ethernet, WiFi 6',
        usb: '4x USB 3.0, 2x USB-C',
      }, null, 2),
    },
  ]);

  const [open, setOpen] = useState(false);
  const [editingBoard, setEditingBoard] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    configuration: '',
  });

  const handleOpen = (board = null) => {
    if (board) {
      setEditingBoard(board);
      setFormData({
        name: board.name,
        configuration: board.configuration,
      });
    } else {
      setEditingBoard(null);
      setFormData({
        name: '',
        configuration: JSON.stringify({
          cpu: '',
          memory: '',
          storage: '',
          network: '',
        }, null, 2),
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingBoard(null);
    setFormData({
      name: '',
      configuration: '',
    });
  };

  const handleSubmit = () => {
    try {
      // Validate JSON
      JSON.parse(formData.configuration);
      
      if (editingBoard) {
        // Update existing board
        setBoards(boards.map(board => 
          board.id === editingBoard.id 
            ? { ...board, ...formData }
            : board
        ));
      } else {
        // Create new board
        const newBoard = {
          id: Date.now(),
          ...formData,
        };
        setBoards([...boards, newBoard]);
      }
      handleClose();
    } catch (error) {
      alert('Invalid JSON configuration. Please check your syntax.');
    }
  };

  const handleDelete = (id) => {
    setBoards(boards.filter(board => board.id !== id));
  };

  const formatConfiguration = (configString) => {
    try {
      const config = JSON.parse(configString);
      return Object.entries(config).map(([key, value]) => (
        <Box key={key} sx={{ mb: 1 }}>
          <Typography variant="body2">
            <strong>{key}:</strong> {value}
          </Typography>
        </Box>
      ));
    } catch (error) {
      return <Typography variant="body2" color="error">Invalid configuration</Typography>;
    }
  };

  return (
    <Box sx={{p:3}}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Board Setup</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add Board
        </Button>
      </Box>

      <Box>
        {boards.map((board) => (
          <Accordion key={board.id} sx={{ mb: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
                <Box>
                  <Typography variant="h6">{board.name}</Typography>
                  <Chip label="Active" color="success" size="small" />
                </Box>
                <Box>
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpen(board);
                    }}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(board.id);
                    }}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle2" gutterBottom>
                Configuration:
              </Typography>
              {formatConfiguration(board.configuration)}
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingBoard ? 'Edit Board' : 'Add New Board'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Board Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Configuration (JSON)"
            fullWidth
            multiline
            rows={10}
            variant="outlined"
            value={formData.configuration}
            onChange={(e) => setFormData({ ...formData, configuration: e.target.value })}
            helperText="Enter board configuration in JSON format"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={!formData.name || !formData.configuration}
          >
            {editingBoard ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BoardSetup;
