import { useState, useEffect } from 'react';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	Button,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	IconButton,
	Chip,
	Alert,
	Snackbar,
	FormControl,
	InputLabel,
	Select,
	MenuItem,
	OutlinedInput,
	Checkbox,
	ListItemText,
	Grid,
	Tabs,
	// CircularProgress,
	Tab,
	CircularProgress,
	Pagination,
} from '@mui/material';
import {
	Add as AddIcon,
	Edit as EditIcon,
	Delete as DeleteIcon,
	Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { testcaseService, boardService } from '../services';

const TabPanel = ({ children, value, index, ...other }) => (
	<div
		role="tabpanel"
		hidden={value !== index}
		id={`simple-tabpanel-${index}`}
		aria-labelledby={`simple-tab-${index}`}
		{...other}
	>
		{value === index && <Box sx={{ p: 3 }}>{children}</Box>}
	</div>
);

const TestCases = () => {
	const rowsPerPage = 20;
	const [testCases, setTestCases] = useState([]);
	const [boardInfos, setBoardInfos] = useState([]);
	const [loading, setLoading] = useState(false);
	const [open, setOpen] = useState(false);
	const [editingTestCase, setEditingTestCase] = useState(null);
	const [tabValue, setTabValue] = useState(0);
	const [filter, setFilter] = useState("");
	const [page, setPage] = useState(1);
	const [count, setCount] = useState(0);

	const [formData, setFormData] = useState({
		testcase_id: '',
		brief: '',
		details: '',
		pre_condition: '',
		post_condition: '',
		test_level: 'System',
		test_type: 'Functional',
		test_technique: 'BlackBox',
		test_procedure: '',
		pass_criteria: '',
		example_log: '',
		note: '',
		requirements: '',
		platform: [],
		hw_depend: '',
		module: '',
		suite: '',
	});
	const [snackbar, setSnackbar] = useState({
		open: false,
		message: '',
		severity: 'success',
	});

	useEffect(() => {
		loadTestCases("change_page");
		loadBoardInfos();
	}, [page]);

	useEffect(() => {
		loadTestCases("change_filter");
		loadBoardInfos();
	}, [filter]);

	const loadTestCases = async (change) => {
		try {
			setLoading(true);
			let data = null;
			if (change === "change_page") {
				data = await testcaseService.getTestCases({
					page: page,
					search: filter 
				});
			} else {
				data = await testcaseService.getTestCases({
					page: 1,
					search: filter 
				});
			}
			setTestCases(data.results || data);
			setCount(parseInt(data.count/rowsPerPage))
		} catch (error) {
			showSnackbar('Error loading test cases: ' + error.message, 'error');
		} finally {
			setLoading(false);
		}
	};

	const loadBoardInfos = async () => {
		try {
			const data = await boardService.getBoardInformation();
			setBoardInfos(data.results || data);
		} catch (error) {
			console.error('Error loading board information:', error);
		}
	};

	const showSnackbar = (message, severity = 'success') => {
		setSnackbar({ open: true, message, severity });
	};

	const handleOpen = (testCase = null) => {
		if (testCase) {
			setEditingTestCase(testCase);
			setFormData({
				testcase_id: testCase.testcase_id,
				brief: testCase.brief || '',
				details: testCase.details || '',
				pre_condition: testCase.pre_condition || '',
				post_condition: testCase.post_condition || '',
				test_level: testCase.test_level || 'System',
				test_type: testCase.test_type || 'Functional',
				test_technique: testCase.test_technique || 'BlackBox',
				test_procedure: testCase.test_procedure || '',
				pass_criteria: testCase.pass_criteria || '',
				example_log: testCase.example_log || '',
				note: testCase.note || '',
				requirements: testCase.requirements || '',
				platform: testCase.platform || [],
				hw_depend: testCase.hw_depend || '',
				module: testCase.module || '',
				suite: testCase.suite || '',
			});
		} else {
			setEditingTestCase(null);
			setFormData({
				testcase_id: '',
				brief: '',
				details: '',
				pre_condition: '',
				post_condition: '',
				test_level: 'System',
				test_type: 'Functional',
				test_technique: 'BlackBox',
				test_procedure: '',
				pass_criteria: '',
				example_log: '',
				note: '',
				requirements: '',
				platform: [],
				hw_depend: '',
				module: '',
				suite: '',
			});
		}
		setTabValue(0);
		setOpen(true);
	};

	const handleClose = () => {
		setOpen(false);
		setEditingTestCase(null);
		setTabValue(0);
	};

	const handleSubmit = async () => {
		console.log("Hello World")
		const validation = testcaseService.validateTestCase(formData);
		if (!validation.isValid) {
			console.log('Validation errors:', validation.errors);
			const firstError = Object.values(validation.errors)[0];
			showSnackbar(firstError, 'error');
			return;
		}

		try {
			setLoading(true);
			console.log('Creating test case:', formData);
			if (editingTestCase) {
				await testcaseService.updateTestCase(editingTestCase.id, formData);
				showSnackbar('Test case updated successfully');
			} else {
				console.log('Creating test case:', formData);
				await testcaseService.createTestCase(formData);
				showSnackbar('Test case created successfully');
			}

			handleClose();
			loadTestCases();
		} catch (error) {
			console.error('Error saving test case:', error);
			showSnackbar('Error saving test case: ' + error.message, 'error');
		} finally {
			console.log('Test case created/updated successfully');
			setLoading(false);
		}
	};

	const handleDelete = async (id) => {
		if (window.confirm('Are you sure you want to delete this test case?')) {
			try {
				setLoading(true);
				await testcaseService.deleteTestCase(id);
				showSnackbar('Test case deleted successfully');
				loadTestCases();
			} catch (error) {
				showSnackbar('Error deleting test case: ' + error.message, 'error');
			} finally {
				setLoading(false);
			}
		}
	};

	const handleChangePage = (_, newPage) => {
		setPage(newPage);
	};

	return (
		<Box sx={{p: 3}}>
			<Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
				<Box display="flex">
					<Typography variant="h4" sx={{mr: 2}}>
						Test Cases
					</Typography>
					<TextField 
						label="Search"
						value={filter}
						onChange={(e) => setFilter(e.target.value)}
						placeholder='Search Testcase/Module/Suite'
						size="small"
					/>
				</Box>
				<Button
					variant="contained"
					startIcon={<AddIcon />}
					onClick={() => handleOpen()}
				>
					Add Test Case
				</Button>
			</Box>

			{loading && (
				<Box display="flex" justifyContent="center" my={2}>
					<CircularProgress />
				</Box>
			)}

			<TableContainer component={Paper}>
				<Table>
					<TableHead>
						<TableRow>
							<TableCell>Test Case ID</TableCell>
							<TableCell>Module</TableCell>
							<TableCell>Suite</TableCell>
							<TableCell>Brief</TableCell>
							{/* <TableCell>Test Level</TableCell> */}
							{/* <TableCell>Test Type</TableCell> */}
							{/* <TableCell>Platform</TableCell> */}
							<TableCell align="center">Actions</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{testCases.length === 0 ? (
							<TableRow>
								<TableCell colSpan={8} align="center">
									<Box py={4}>
										<AssignmentIcon color="disabled" sx={{ fontSize: 48, mb: 2 }} />
										<Typography variant="body1" color="textSecondary">
											No test cases found. Create your first test case.
										</Typography>
									</Box>
								</TableCell>
							</TableRow>
						) : (
							testCases.map((testCase) => (
								<TableRow key={testCase.id}>
									<TableCell>
										<Chip
											label={testCase.testcase_id}
											color="primary"
											variant="outlined"
										/>
									</TableCell>
									<TableCell>
										<Typography variant="body2">
											{testCase.module || '-'}
										</Typography>
									</TableCell>
									<TableCell>
										<Typography variant="body2">
											{testCase.suite || '-'}
										</Typography>
									</TableCell>
									<TableCell>
										<Typography variant="body2" sx={{ maxWidth: 200 }}>
											{testCase.brief && testCase.brief.length > 50
												? `${testCase.brief.substring(0, 50)}...`
												: testCase.brief || 'No brief'}
										</Typography>
									</TableCell>
									{/* <TableCell>
										<Chip
											label={testCase.test_level || 'System'}
											color="default"
											size="small"
										/>
									</TableCell> */}
									{/* <TableCell>
										<Chip
											label={testCase.test_type || 'Functional'}
											color="primary"
											size="small"
										/>
									</TableCell> */}
									{/* <TableCell>
										<Chip
											label={testCase.test_technique || 'BlackBox'}
											color="secondary"
											size="small"
										/>
									</TableCell> */}
									{/* <TableCell>
										<Typography variant="body2" sx={{ maxWidth: 150 }}>
											{testCase.platform || 'No platform'}
										</Typography>
									</TableCell> */}
									<TableCell align="center">
										<IconButton
											size="small"
											onClick={() => handleOpen(testCase)}
											color="primary"
										>
											<EditIcon />
										</IconButton>
										<IconButton
											size="small"
											onClick={() => handleDelete(testCase.id)}
											color="error"
										>
											<DeleteIcon />
										</IconButton>
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</TableContainer>

			{count > 1 && (
			<Box display="flex" justifyContent="center" mt={2}>
				<Pagination
					count={count}
					page={page}
					onChange={handleChangePage}
					color="primary"
				/>
			</Box>
			)}

			<Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
				<DialogTitle>
					{editingTestCase ? 'Edit Test Case' : 'Add New Test Case'}
				</DialogTitle>
				<DialogContent>
					<Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 3 }}>
						<Tab label="Basic Info" />
						<Tab label="Procedure & Criteria" />
						<Tab label="Conditions & Notes" />
					</Tabs>

					<TabPanel value={tabValue} index={0}>
						<Grid container spacing={2}>
							<Grid size={{ xs: 12, md: 6 }}>
								<TextField
									autoFocus
									margin="dense"
									label="Test Case ID"
									fullWidth
									variant="outlined"
									value={formData.testcase_id}
									onChange={(e) => setFormData({ ...formData, testcase_id: e.target.value })}
									required
								/>
							</Grid>
							<Grid size={{ xs: 12, md: 6 }}>
								<FormControl fullWidth margin="dense">
									<InputLabel>Test Level</InputLabel>
									<Select
										value={formData.test_level}
										label="Test Level"
										onChange={(e) => setFormData({ ...formData, test_level: e.target.value })}
									>
										<MenuItem value="Unit">Unit</MenuItem>
										<MenuItem value="Integration">Integration</MenuItem>
										<MenuItem value="System">System</MenuItem>
										<MenuItem value="Acceptance">Acceptance</MenuItem>
									</Select>
								</FormControl>
							</Grid>
							<Grid size={{ xs: 12, md: 6 }}>
								<FormControl fullWidth margin="dense">
									<InputLabel>Test Type</InputLabel>
									<Select
										value={formData.test_type}
										label="Test Type"
										onChange={(e) => setFormData({ ...formData, test_type: e.target.value })}
									>
										<MenuItem value="Functional">Functional</MenuItem>
										<MenuItem value="Non-Functional">Non-Functional</MenuItem>
										<MenuItem value="Performance">Performance</MenuItem>
										<MenuItem value="Security">Security</MenuItem>
										<MenuItem value="Usability">Usability</MenuItem>
										<MenuItem value="Compatibility">Compatibility</MenuItem>
									</Select>
								</FormControl>
							</Grid>
							<Grid size={{ xs: 12, md: 6 }}>
								<FormControl fullWidth margin="dense">
									<InputLabel>Test Technique</InputLabel>
									<Select
										value={formData.test_technique}
										label="Test Technique"
										onChange={(e) => setFormData({ ...formData, test_technique: e.target.value })}
									>
										<MenuItem value="BlackBox">Black Box</MenuItem>
										<MenuItem value="WhiteBox">White Box</MenuItem>
										<MenuItem value="GreyBox">Grey Box</MenuItem>
									</Select>
								</FormControl>
							</Grid>
							<Grid size={{ xs: 12, md: 6 }}>
								<TextField
									margin="dense"
									label="Module"
									fullWidth
									variant="outlined"
									value={formData.module}
									onChange={(e) => setFormData({ ...formData, module: e.target.value })}
									placeholder="Enter module name..."
								/>
							</Grid>
							<Grid size={{ xs: 12, md: 6 }}>
								<TextField
									margin="dense"
									label="Suite"
									fullWidth
									variant="outlined"
									value={formData.suite}
									onChange={(e) => setFormData({ ...formData, suite: e.target.value })}
									placeholder="Enter test suite name..."
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Brief Description"
									fullWidth
									// multiline
									// rows={3}
									variant="outlined"
									value={formData.brief}
									onChange={(e) => setFormData({ ...formData, brief: e.target.value })}
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Details"
									fullWidth
									multiline
									rows={4}
									variant="outlined"
									value={formData.details}
									onChange={(e) => setFormData({ ...formData, details: e.target.value })}
								/>
							</Grid>
						</Grid>
					</TabPanel>

					<TabPanel value={tabValue} index={1}>
						<Grid container spacing={2}>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Test Procedure"
									fullWidth
									multiline
									rows={4}
									variant="outlined"
									value={formData.test_procedure}
									onChange={(e) => setFormData({ ...formData, test_procedure: e.target.value })}
									placeholder="Describe the step-by-step test procedure..."
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Pass Criteria"
									fullWidth
									multiline
									rows={3}
									variant="outlined"
									value={formData.pass_criteria}
									onChange={(e) => setFormData({ ...formData, pass_criteria: e.target.value })}
									placeholder="Define the criteria for test to pass..."
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Example Log"
									fullWidth
									multiline
									rows={4}
									variant="outlined"
									value={formData.example_log}
									onChange={(e) => setFormData({ ...formData, example_log: e.target.value })}
									placeholder="Provide example log output..."
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Requirements"
									fullWidth
									multiline
									rows={3}
									variant="outlined"
									value={formData.requirements}
									onChange={(e) => setFormData({ ...formData, requirements: e.target.value })}
									placeholder="List requirements and dependencies..."
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<FormControl fullWidth variant="outlined" margin="dense">
									<InputLabel>Platform</InputLabel>
									<Select
										multiple
										value={formData.platform}
										onChange={(e) => setFormData({ ...formData, platform: e.target.value })}
										input={<OutlinedInput label="Platform" />}
										renderValue={(selected) => (
											<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
												{selected.map((value) => {
													const board = boardInfos.find(b => b.id === value);
													return (
														<Chip key={value} label={board ? board.name : 'Unknown'} size="small" />
													);
												})}
											</Box>
										)}
									>
										{boardInfos.map((board) => (
											<MenuItem key={board.id} value={board.id}>
												<Checkbox checked={formData.platform.indexOf(board.id) > -1} />
												<ListItemText primary={board.name} />
											</MenuItem>
										))}
									</Select>
								</FormControl>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Hardware Dependencies"
									fullWidth
									multiline
									rows={3}
									variant="outlined"
									value={formData.hw_depend}
									onChange={(e) => setFormData({ ...formData, hw_depend: e.target.value })}
									placeholder="Describe hardware dependencies..."
								/>
							</Grid>
						</Grid>
					</TabPanel>

					<TabPanel value={tabValue} index={2}>
						<Grid container spacing={2}>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Pre-condition"
									fullWidth
									multiline
									rows={4}
									variant="outlined"
									value={formData.pre_condition}
									onChange={(e) => setFormData({ ...formData, pre_condition: e.target.value })}
									placeholder="Describe conditions that must be met before test execution..."
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Post-condition"
									fullWidth
									multiline
									rows={4}
									variant="outlined"
									value={formData.post_condition}
									onChange={(e) => setFormData({ ...formData, post_condition: e.target.value })}
									placeholder="Describe expected state after test execution..."
								/>
							</Grid>
							<Grid size={{ xs: 12 }}>
								<TextField
									margin="dense"
									label="Note"
									fullWidth
									multiline
									rows={3}
									variant="outlined"
									value={formData.note}
									onChange={(e) => setFormData({ ...formData, note: e.target.value })}
									placeholder="Additional notes or comments..."
								/>
							</Grid>
						</Grid>
					</TabPanel>


				</DialogContent>
				<DialogActions>
					<Button onClick={handleClose}>Cancel</Button>
					<Button
						onClick={handleSubmit}
						variant="contained"
						disabled={!formData.testcase_id.trim() || !formData.brief.trim()}
					>
						{editingTestCase ? 'Update' : 'Create'}
					</Button>
				</DialogActions>
			</Dialog>

			<Snackbar
				open={snackbar.open}
				autoHideDuration={6000}
				onClose={() => setSnackbar({ ...snackbar, open: false })}
			>
				<Alert
					onClose={() => setSnackbar({ ...snackbar, open: false })}
					severity={snackbar.severity}
					sx={{ width: '100%' }}
				>
					{snackbar.message}
				</Alert>
			</Snackbar>
		</Box>
	);
};

export default TestCases;
