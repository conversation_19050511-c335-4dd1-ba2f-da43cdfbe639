import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
	Typo<PERSON>,
	Button,
	Box,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	MenuItem,
	IconButton,
	Paper,
	InputAdornment,
	Chip,
	FormControl,
	InputLabel,
	Select,
	Alert,
	Snackbar,
	Grid,
	LinearProgress,
	Autocomplete,
	Checkbox,
	CircularProgress,
	ListItemText,
	Accordion,
	AccordionSummary,
	AccordionDetails,
	Tabs,
	Tab,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Breadcrumbs,
	Link,
} from "@mui/material";
import {
	Add as AddIcon,
	Edit as EditIcon,
	Delete as DeleteIcon,
	PlayArrow as PlayIcon,
	ExpandMore as ExpandMoreIcon,
	ArrowBack as ArrowBackIcon,
	Assignment as AssignmentIcon,
	Timeline as TimelineIcon,
	Schedule as ScheduleIcon,
	Cancel as CancelIcon,
	Search as SearchIcon,
	Clear as ClearIcon,
} from "@mui/icons-material";
import {
	testingPlanTotalService,
	testcaseService,
	boardService,
} from "../services";

const TestingPlanDetail = () => {
	const { id } = useParams();
	const navigate = useNavigate();

	const [testingPlanTotal, setTestingPlanTotal] = useState(null);
	const [testingPlans, setTestingPlans] = useState([]);
	const [planBatches, setPlanBatches] = useState({}); // Store batches by plan ID
	const [batchRuns, setBatchRuns] = useState({}); // Store runs by batch ID
	const [batchResults, setBatchResults] = useState({}); // Store results by batch ID
	const [boardInformation, setBoardInformation] = useState([]);
	const [choices, setChoices] = useState({});
	const [loading, setLoading] = useState(false);
	const [tabValue, setTabValue] = useState(0);
	const [searchQuery, setSearchQuery] = useState("");

	// New states for dynamic search
	const [dynamicModuleOptions, setDynamicModuleOptions] = useState([]);
	const [dynamicSuiteOptions, setDynamicSuiteOptions] = useState([]);
	const [dynamicTestCaseOptions, setDynamicTestCaseOptions] = useState([]);
	const [moduleSearchTerm, setModuleSearchTerm] = useState("");
	const [suiteSearchTerm, setSuiteSearchTerm] = useState("");
	const [testCaseSearchTerm, setTestCaseSearchTerm] = useState("");
	const [selectedModules, setSelectedModules] = useState([]);
	const [selectedSuites, setSelectedSuites] = useState([]);

	// Loading states
	const [loadingExistingTestCases, setLoadingExistingTestCases] = useState(false);
	const [loadingBatches, setLoadingBatches] = useState({});

	// Track which testcases belong to which module/suite
	const [moduleTestCaseMapping, setModuleTestCaseMapping] = useState({});
	const [suiteTestCaseMapping, setSuiteTestCaseMapping] = useState({});

	// Accordion states
	const [expandedPlan, setExpandedPlan] = useState(false);
	const [expandedBatch, setExpandedBatch] = useState({});

	// Dialog states
	const [planDialogOpen, setPlanDialogOpen] = useState(false);
	const [batchDialogOpen, setBatchDialogOpen] = useState(false);

	const [editingPlan, setEditingPlan] = useState(null);
	const [editingBatch, setEditingBatch] = useState(null);
	const [selectedPlan, setSelectedPlan] = useState(null);

	// Form data
	const [planFormData, setPlanFormData] = useState({
		name: "",
		description: "{}",
		priority: 100,
		status: "Draft",
		board_information: "",
		test_cases: [],
	});

	const [batchFormData, setBatchFormData] = useState({
		name: "",
		status: "Pending",
		test_cases: [],
		selected_modules: [],
		selected_suites: [],
		order: 1,
	});

	const [snackbar, setSnackbar] = useState({
		open: false,
		message: "",
		severity: "info",
	});

	useEffect(() => {
		if (id) {
			loadTestingPlanTotal();
			loadTestingPlans();
			loadBoardInformation();
			loadChoices();
		}

		// Cleanup loading states on unmount
		return () => {
			setLoadingBatches({});
		};
	}, [id]);

	const loadTestingPlanTotal = async () => {
		try {
			setLoading(true);
			const data = await testingPlanTotalService.getTestingPlanTotal(id);
			setTestingPlanTotal(data);
		} catch (error) {
			showSnackbar(
				"Error loading testing plan total: " + error.message,
				"error"
			);
		} finally {
			setLoading(false);
		}
	};

	const loadTestingPlans = async () => {
		try {
			const data = await testingPlanTotalService.getTestingPlans({
				testing_plan_total: id,
			});
			setTestingPlans(data?.results || []);
			console.log(data.results);
		} catch (error) {
			showSnackbar("Error loading testing plans: " + error.message, "error");
		}
	};

	const loadTestingPlanBatches = async (planId) => {
		try {
			setLoadingBatches(prev => ({ ...prev, [planId]: true }));
			console.log(`Loading batches for plan ${planId}...`);

			const data = await testingPlanTotalService.getTestingPlanBatches(planId);
			setPlanBatches((prev) => ({
				...prev,
				[planId]: data?.results || [],
			}));

			console.log(`Loaded ${(data?.results || []).length} batches for plan ${planId}`);
		} catch (error) {
			showSnackbar(
				"Error loading testing plan batches: " + error.message,
				"error"
			);
		} finally {
			setLoadingBatches(prev => ({ ...prev, [planId]: false }));
		}
	};

	const loadTestingPlanRuns = async (batchId) => {
		try {
			const data = await testingPlanTotalService.getTestingPlanRuns(batchId);
			setBatchRuns((prev) => ({
				...prev,
				[batchId]: data?.results || [],
			}));
		} catch (error) {
			showSnackbar(
				"Error loading testing plan runs: " + error.message,
				"error"
			);
		}
	};

	const loadBatchResults = async (batchId) => {
		try {
			// Get all runs for this batch first
			const runsData = await testingPlanTotalService.getTestingPlanRuns(
				batchId
			);
			const runs = runsData?.results || [];

			// Load results for all runs in this batch
			const allResults = [];
			for (const run of runs) {
				try {
					const resultsData = await testingPlanTotalService.getResults(run.id);
					const results = resultsData?.results || [];
					allResults.push(...results);
				} catch (error) {
					console.error(`Error loading results for run ${run.id}:`, error);
				}
			}

			setBatchResults((prev) => ({
				...prev,
				[batchId]: allResults,
			}));
		} catch (error) {
			showSnackbar("Error loading batch results: " + error.message, "error");
		}
	};



	const loadBoardInformation = async () => {
		try {
			const data = await boardService.getBoardInformation();
			setBoardInformation(data.results || []);
		} catch (error) {
			console.error("Error loading board information:", error);
		}
	};

	const loadChoices = async () => {
		try {
			const data = await testingPlanTotalService.getChoices();
			setChoices(data || {});
		} catch (error) {
			console.error("Error loading choices:", error);
		}
	};

	// Dynamic search functions
	const searchModules = async (searchTerm) => {
		if (!searchTerm || searchTerm.length < 1) {
			setDynamicModuleOptions([]);
			return;
		}
		try {
			const data = await testcaseService.searchTestCases(searchTerm);
			const testCases = data.results || data;
			// Extract unique modules
			const modules = [...new Set(testCases.map(tc => tc.module).filter(Boolean))];
			setDynamicModuleOptions(modules.map(module => ({ label: module, value: module })));
		} catch (error) {
			console.error("Error searching modules:", error);
		}
	};

	const searchSuites = async (searchTerm) => {
		if (!searchTerm || searchTerm.length < 1) {
			setDynamicSuiteOptions([]);
			return;
		}
		try {
			const data = await testcaseService.searchTestCases(searchTerm);
			const testCases = data.results || data;
			// Extract unique suites
			const suites = [...new Set(testCases.map(tc => tc.suite).filter(Boolean))];
			setDynamicSuiteOptions(suites.map(suite => ({ label: suite, value: suite })));
		} catch (error) {
			console.error("Error searching suites:", error);
		}
	};

	const searchTestCases = async (searchTerm) => {
		if (!searchTerm || searchTerm.length < 1) {
			// Don't clear existing options, just keep the ones already selected
			const selectedTestCaseIds = batchFormData.test_cases;
			if (selectedTestCaseIds.length > 0) {
				// Keep only the testcases that are currently selected
				setDynamicTestCaseOptions(prev =>
					prev.filter(tc => selectedTestCaseIds.includes(tc.value))
				);
			} else {
				setDynamicTestCaseOptions([]);
			}
			return;
		}
		try {
			const data = await testcaseService.searchTestCases(searchTerm);
			const testCases = data.results || data;
			const searchResults = testCases.map(tc => ({
				label: `${tc.testcase_id} - ${tc.brief || 'No description'}`,
				value: tc.id,
				testCase: tc
			}));

			// Merge with existing selected testcases
			setDynamicTestCaseOptions(prev => {
				const selectedTestCaseIds = batchFormData.test_cases;
				const existingSelected = prev.filter(tc => selectedTestCaseIds.includes(tc.value));
				const existingIds = existingSelected.map(tc => tc.value);
				const newSearchResults = searchResults.filter(tc => !existingIds.includes(tc.value));
				return [...existingSelected, ...newSearchResults];
			});
		} catch (error) {
			console.error("Error searching test cases:", error);
		}
	};

	// Handle module selection - add all testcases from selected modules
	const handleModuleSelection = async (selectedModuleValues) => {
		try {
			const allTestCases = [];
			for (const moduleValue of selectedModuleValues) {
				// Load ALL testcases for this module (no pagination limit)
				const data = await testcaseService.getTestCases({
					module: moduleValue,  // Use specific module filter instead of search
					all: "true"  // Get all testcases, not just first page
				});
				const moduleTCs = (data.results || data).filter(tc => tc.module === moduleValue);
				console.log(`Module "${moduleValue}" has ${moduleTCs.length} testcases`);
				console.log(`API returned ${(data.results || data).length} total results`);
				allTestCases.push(...moduleTCs);
			}

			// Add to current selection
			const newTestCaseIds = allTestCases.map(tc => tc.id);
			console.log(`Total testcases loaded from modules: ${allTestCases.length}`);
			console.log(`New testcase IDs: ${newTestCaseIds.length}`);

			setBatchFormData(prev => ({
				...prev,
				test_cases: [...new Set([...prev.test_cases, ...newTestCaseIds])],
				selected_modules: [...new Set([...prev.selected_modules, ...selectedModuleValues])] // Save selected modules
			}));

			// Update module-testcase mapping
			setModuleTestCaseMapping(prev => {
				const newMapping = { ...prev };
				selectedModuleValues.forEach(moduleValue => {
					const moduleTestCases = allTestCases.filter(tc => tc.module === moduleValue).map(tc => tc.id);
					newMapping[moduleValue] = [...new Set([...(newMapping[moduleValue] || []), ...moduleTestCases])];
				});
				return newMapping;
			});

			// Also add to dynamic options so they appear in autocomplete
			const newTestCaseOptions = allTestCases.map(tc => ({
				label: `${tc.testcase_id} - ${tc.brief || 'No description'}`,
				value: tc.id,
				testCase: tc
			}));

			setDynamicTestCaseOptions(prev => {
				const existingIds = prev.map(tc => tc.value);
				const uniqueNewOptions = newTestCaseOptions.filter(tc => !existingIds.includes(tc.value));
				return [...prev, ...uniqueNewOptions];
			});
		} catch (error) {
			console.error("Error loading test cases from modules:", error);
		}
	};

	// Handle suite selection - add all testcases from selected suites
	const handleSuiteSelection = async (selectedSuiteValues) => {
		try {
			const allTestCases = [];
			for (const suiteValue of selectedSuiteValues) {
				// Load ALL testcases for this suite (no pagination limit)
				const data = await testcaseService.getTestCases({
					suite: suiteValue,  // Use specific suite filter instead of search
					all: "true"  // Get all testcases, not just first page
				});
				const suiteTCs = (data.results || data).filter(tc => tc.suite === suiteValue);
				console.log(`Suite "${suiteValue}" has ${suiteTCs.length} testcases`);
				console.log(`API returned ${(data.results || data).length} total results`);
				allTestCases.push(...suiteTCs);
			}

			// Add to current selection
			const newTestCaseIds = allTestCases.map(tc => tc.id);
			console.log(`Total testcases loaded from suites: ${allTestCases.length}`);
			console.log(`New testcase IDs: ${newTestCaseIds.length}`);

			setBatchFormData(prev => ({
				...prev,
				test_cases: [...new Set([...prev.test_cases, ...newTestCaseIds])],
				selected_suites: [...new Set([...prev.selected_suites, ...selectedSuiteValues])] // Save selected suites
			}));

			// Update suite-testcase mapping
			setSuiteTestCaseMapping(prev => {
				const newMapping = { ...prev };
				selectedSuiteValues.forEach(suiteValue => {
					const suiteTestCases = allTestCases.filter(tc => tc.suite === suiteValue).map(tc => tc.id);
					newMapping[suiteValue] = [...new Set([...(newMapping[suiteValue] || []), ...suiteTestCases])];
				});
				return newMapping;
			});

			// Also add to dynamic options so they appear in autocomplete
			const newTestCaseOptions = allTestCases.map(tc => ({
				label: `${tc.testcase_id} - ${tc.brief || 'No description'}`,
				value: tc.id,
				testCase: tc
			}));

			setDynamicTestCaseOptions(prev => {
				const existingIds = prev.map(tc => tc.value);
				const uniqueNewOptions = newTestCaseOptions.filter(tc => !existingIds.includes(tc.value));
				return [...prev, ...uniqueNewOptions];
			});
		} catch (error) {
			console.error("Error loading test cases from suites:", error);
		}
	};

	// Load existing testcases when editing batch
	const loadExistingTestCases = async (testCaseIds) => {
		if (!testCaseIds || testCaseIds.length === 0) return;

		try {
			setLoadingExistingTestCases(true);
			console.log(`Loading ${testCaseIds.length} existing testcases...`);

			const existingTestCases = [];
			// Load testcases by their IDs
			for (const tcId of testCaseIds) {
				try {
					const testCase = await testcaseService.getTestCase(tcId);
					existingTestCases.push({
						label: `${testCase.testcase_id} - ${testCase.brief || 'No description'}`,
						value: testCase.id,
						testCase: testCase
					});
				} catch (error) {
					console.error(`Error loading testcase ${tcId}:`, error);
				}
			}

			console.log(`Loaded ${existingTestCases.length} existing testcases`);
			// Replace dynamic options with existing testcases (don't merge to avoid duplicates)
			setDynamicTestCaseOptions(existingTestCases);
		} catch (error) {
			console.error("Error loading existing test cases:", error);
		} finally {
			setLoadingExistingTestCases(false);
		}
	};

	// Handle removing a module and its testcases
	const handleRemoveModule = async (moduleToRemove) => {
		try {
			console.log("Removing module:", moduleToRemove);

			// Get testcase IDs from mapping
			const moduleTestCaseIds = moduleTestCaseMapping[moduleToRemove] || [];
			console.log("Module testcase IDs to remove:", moduleTestCaseIds);

			// Remove module from selected modules
			const updatedModules = selectedModules.filter(m => m.value !== moduleToRemove);
			setSelectedModules(updatedModules);
			console.log("Updated selected modules:", updatedModules);

			// Remove testcases from this module
			setBatchFormData(prev => ({
				...prev,
				test_cases: prev.test_cases.filter(id => !moduleTestCaseIds.includes(id)),
				selected_modules: prev.selected_modules.filter(module => module !== moduleToRemove)
			}));

			// Remove from dynamic testcase options
			setDynamicTestCaseOptions(prev => {
				const filtered = prev.filter(tc => !moduleTestCaseIds.includes(tc.value));
				console.log("Updated testcase options after module removal:", filtered.length);
				return filtered;
			});

			// Remove from dynamic module options
			setDynamicModuleOptions(prev => {
				const filtered = prev.filter(m => m.value !== moduleToRemove);
				console.log("Updated module options after removal:", filtered);
				return filtered;
			});

			// Remove from mapping
			setModuleTestCaseMapping(prev => {
				const newMapping = { ...prev };
				delete newMapping[moduleToRemove];
				return newMapping;
			});
		} catch (error) {
			console.error("Error removing module:", error);
		}
	};

	// Handle removing a suite and its testcases
	const handleRemoveSuite = async (suiteToRemove) => {
		try {
			console.log("Removing suite:", suiteToRemove);

			// Get testcase IDs from mapping
			const suiteTestCaseIds = suiteTestCaseMapping[suiteToRemove] || [];
			console.log("Suite testcase IDs to remove:", suiteTestCaseIds);

			// Remove suite from selected suites
			const updatedSuites = selectedSuites.filter(s => s.value !== suiteToRemove);
			setSelectedSuites(updatedSuites);
			console.log("Updated selected suites:", updatedSuites);

			// Remove testcases from this suite
			setBatchFormData(prev => ({
				...prev,
				test_cases: prev.test_cases.filter(id => !suiteTestCaseIds.includes(id)),
				selected_suites: prev.selected_suites.filter(suite => suite !== suiteToRemove)
			}));

			// Remove from dynamic testcase options
			setDynamicTestCaseOptions(prev => {
				const filtered = prev.filter(tc => !suiteTestCaseIds.includes(tc.value));
				console.log("Updated testcase options after suite removal:", filtered.length);
				return filtered;
			});

			// Remove from dynamic suite options
			setDynamicSuiteOptions(prev => {
				const filtered = prev.filter(s => s.value !== suiteToRemove);
				console.log("Updated suite options after removal:", filtered);
				return filtered;
			});

			// Remove from mapping
			setSuiteTestCaseMapping(prev => {
				const newMapping = { ...prev };
				delete newMapping[suiteToRemove];
				return newMapping;
			});
		} catch (error) {
			console.error("Error removing suite:", error);
		}
	};

	const showSnackbar = (message, severity = "info") => {
		setSnackbar({ open: true, message, severity });
	};

	// Testing Plan CRUD
	const handleOpenPlanDialog = (plan = null) => {
		if (plan) {
			setEditingPlan(plan);
			setPlanFormData({
				name: plan.name,
				description:
					typeof plan.description === "object"
						? JSON.stringify(plan.description, null, 2)
						: plan.description,
				priority: plan.priority || 100,
				status: plan.status,
				board_information: plan.board_information || "",
				test_cases: plan.test_cases || [],
			});
		} else {
			setEditingPlan(null);
			setPlanFormData({
				name: "",
				description: "{}",
				priority: 100,
				status: "Draft",
				board_information: "",
				test_cases: [],
			});
		}
		setPlanDialogOpen(true);
	};

	const handleSubmitPlan = async () => {
		if (!planFormData.name.trim()) {
			showSnackbar("Plan name is required", "error");
			return;
		}

		try {
			const submitData = {
				...planFormData,
				testing_plan_total: parseInt(id),
				description: planFormData.description.trim()
					? JSON.parse(planFormData.description)
					: {},
			};

			if (editingPlan) {
				await testingPlanTotalService.updateTestingPlan(
					editingPlan.id,
					submitData
				);
				showSnackbar("Testing plan updated successfully", "success");
			} else {
				await testingPlanTotalService.createTestingPlan(submitData);
				showSnackbar("Testing plan created successfully", "success");
			}

			setPlanDialogOpen(false);
			loadTestingPlans();
		} catch (error) {
			showSnackbar("Error saving testing plan: " + error.message, "error");
		}
	};

	const handleDeletePlan = async (planId) => {
		if (window.confirm("Are you sure you want to delete this testing plan?")) {
			try {
				await testingPlanTotalService.deleteTestingPlan(planId);
				showSnackbar("Testing plan deleted successfully", "success");
				loadTestingPlans();
			} catch (error) {
				showSnackbar("Error deleting testing plan: " + error.message, "error");
			}
		}
	};

	const handleTriggerJenkinsPlan = async (planId, planName) => {
		if (
			window.confirm(
				`Are you sure you want to trigger Jenkins build for "${planName}"?`
			)
		) {
			try {
				await testingPlanTotalService.triggerJenkinsBuild(planId);
				showSnackbar("Jenkins build triggered successfully", "success");
				loadTestingPlans();
			} catch (error) {
				showSnackbar(
					"Error triggering Jenkins build: " + error.message,
					"error"
				);
			}
		}
	};

	// Testing Plan Batch CRUD
	const handleSubmitBatch = async () => {
		if (!batchFormData.name.trim() || !selectedPlan) {
			showSnackbar("Batch name is required", "error");
			return;
		}

		try {
			const submitData = {
				name: batchFormData.name,
				status: batchFormData.status,
				test_cases: batchFormData.test_cases,
				selected_modules: Array.isArray(batchFormData.selected_modules)
					? batchFormData.selected_modules
					: [],
				selected_suites: Array.isArray(batchFormData.selected_suites)
					? batchFormData.selected_suites
					: [],
				order: batchFormData.order,
			};

			console.log("Submitting batch data:", submitData); // Debug log
			console.log("Current batchFormData:", batchFormData); // Debug log
			console.log("Selected modules state:", selectedModules); // Debug log
			console.log("Selected suites state:", selectedSuites); // Debug log

			if (editingBatch) {
				await testingPlanTotalService.updateTestingPlanBatch(
					selectedPlan.id,
					editingBatch.id,
					submitData
				);
				showSnackbar("Testing plan batch updated successfully", "success");
			} else {
				await testingPlanTotalService.createTestingPlanBatch(
					selectedPlan.id,
					submitData
				);
				showSnackbar("Testing plan batch created successfully", "success");
			}

			setBatchDialogOpen(false);
			setEditingBatch(null);
			loadTestingPlanBatches(selectedPlan.id);
		} catch (error) {
			showSnackbar(
				"Error saving testing plan batch: " + error.message,
				"error"
			);
		}
	};

	const handleDeleteBatch = async (planId, batchId) => {
		if (
			window.confirm("Are you sure you want to delete this testing plan batch?")
		) {
			try {
				await testingPlanTotalService.deleteTestingPlanBatch(planId, batchId);
				showSnackbar("Testing plan batch deleted successfully", "success");
				loadTestingPlanBatches(planId);
			} catch (error) {
				showSnackbar(
					"Error deleting testing plan batch: " + error.message,
					"error"
				);
			}
		}
	};

	const getStatusColor = (status) => {
		const colors = {
			Draft: "default",
			Ready: "info",
			Running: "warning",
			Completed: "success",
			Failed: "error",
			Cancelled: "secondary",
		};
		return colors[status] || "default";
	};

	const getPriorityColor = (priority) => {
		const colors = {
			Low: "success",
			Medium: "warning",
			High: "error",
			Critical: "error",
		};
		return colors[priority] || "default";
	};

	const getBuildColor = (status) => {
		const colors = {
			SUCCESS: "success",
			FAILURE: "error",
			UNSTABLE: "info",
			NOT_BUILT: "secondary",
			ABORTED: "inherite",
		};
		return colors[status] || "primary";
	};

	const getResultColor = (result) => {
		switch (result) {
			case "Pass":
				return "success";
			case "Fail":
				return "error";
			case "Skip":
				return "warning";
			default:
				return "default";
		}
	};

	// Reset search states when dialog opens/closes and load existing testcases when editing
	useEffect(() => {
		if (!batchDialogOpen) {
			setModuleSearchTerm("");
			setSuiteSearchTerm("");
			setTestCaseSearchTerm("");
			setSelectedModules([]);
			setSelectedSuites([]);
			setDynamicModuleOptions([]);
			setDynamicSuiteOptions([]);
			setDynamicTestCaseOptions([]);
			setModuleTestCaseMapping({});
			setSuiteTestCaseMapping({});
			setLoadingExistingTestCases(false);
		}
	}, [batchDialogOpen]);

	// Load existing testcases when editing batch
	useEffect(() => {
		if (batchDialogOpen && editingBatch && batchFormData.test_cases.length > 0) {
			console.log("Loading existing testcases for edit batch...");
			loadExistingTestCases(batchFormData.test_cases);
		}
	}, [batchDialogOpen, editingBatch]);

	// Load selected modules and suites when editing
	useEffect(() => {
		if (batchDialogOpen && editingBatch) {
			// Set selected modules
			if (batchFormData.selected_modules && batchFormData.selected_modules.length > 0) {
				const moduleOptions = batchFormData.selected_modules.map(module => ({
					label: module,
					value: module
				}));
				setSelectedModules(moduleOptions);
				setDynamicModuleOptions(prev => {
					const existingIds = prev.map(m => m.value);
					const newOptions = moduleOptions.filter(m => !existingIds.includes(m.value));
					return [...prev, ...newOptions];
				});
			}

			// Set selected suites
			if (batchFormData.selected_suites && batchFormData.selected_suites.length > 0) {
				const suiteOptions = batchFormData.selected_suites.map(suite => ({
					label: suite,
					value: suite
				}));
				setSelectedSuites(suiteOptions);
				setDynamicSuiteOptions(prev => {
					const existingIds = prev.map(s => s.value);
					const newOptions = suiteOptions.filter(s => !existingIds.includes(s.value));
					return [...prev, ...newOptions];
				});
			}
		}
	}, [batchDialogOpen, editingBatch, batchFormData.selected_modules, batchFormData.selected_suites]);

	const calculateBatchStats = (batch) => {
		const results = batchResults[batch.id] || [];
		const testCaseIds = batch.test_cases || [];

		const stats = {
			total: testCaseIds.length,
			pass: 0,
			fail: 0,
			skip: 0,
			notTested: 0,
		};

		// Count results for each test case
		testCaseIds.forEach((testCaseId) => {
			const testCaseResults = results.filter((r) => r.test_case === testCaseId);
			if (testCaseResults.length === 0) {
				stats.notTested++;
			} else {
				// Get the latest result for this test case
				const latestResult = testCaseResults[testCaseResults.length - 1];
				switch (latestResult.result) {
					case "Pass":
						stats.pass++;
						break;
					case "Fail":
						stats.fail++;
						break;
					case "Skip":
						stats.skip++;
						break;
					default:
						stats.notTested++;
						break;
				}
			}
		});

		return stats;
	};

	// Filter testing plans based on search query
	const filteredTestingPlans = testingPlans.filter((plan) => {
		if (!searchQuery.trim()) return true;

		const query = searchQuery.toLowerCase();

		// Search by plan name
		if (plan.name.toLowerCase().includes(query)) return true;

		// Search by board information name
		if (plan.board_information_detail?.name?.toLowerCase().includes(query))
			return true;

		return false;
	});

	// Helper function to highlight search terms
	const highlightSearchTerm = (text, searchTerm) => {
		if (!searchTerm.trim() || !text) return text;

		const regex = new RegExp(
			`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
			"gi"
		);
		const parts = text.split(regex);

		return parts.map((part, index) =>
			regex.test(part) ? (
				<Box
					component="span"
					key={index}
					sx={{ backgroundColor: "yellow", fontWeight: "bold" }}
				>
					{part}
				</Box>
			) : (
				part
			)
		);
	};

	if (loading && !testingPlanTotal) {
		return <LinearProgress />;
	}

	if (!testingPlanTotal) {
		return (
			<Box>
				<Typography variant="h6">Testing Plan Total not found</Typography>
				<Button onClick={() => navigate("/testing-plan-dashboard")}>
					Back to Dashboard
				</Button>
			</Box>
		);
	}

	const handleNavigateJenkins = (jenkins_url) => {
		window.open(jenkins_url, "_blank");
	};

	const convertDate = (dateString) => {
		if (!dateString) return "";

		const date = new Date(dateString);

		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, "0"); // month is 0-indexed
		const day = String(date.getDate()).padStart(2, "0");
		const hours = String(date.getHours()).padStart(2, "0");
		const minutes = String(date.getMinutes()).padStart(2, "0");
		const seconds = String(date.getSeconds()).padStart(2, "0");

		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	};

	return (
		<Box sx={{ p: 3 }}>
			{/* Header */}
			<Box mb={3}>
				<Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
					<Link
						color="inherit"
						href="#"
						onClick={() => navigate("/testing-plan-dashboard")}
						sx={{ display: "flex", alignItems: "center" }}
					>
						Testing Plan Dashboard
					</Link>
					<Typography color="text.primary">{testingPlanTotal.name}</Typography>
				</Breadcrumbs>

				<Box display="flex" justifyContent="space-between" alignItems="center">
					<Box>
						<Typography variant="h4" gutterBottom>
							{testingPlanTotal.name}
						</Typography>
						<Box display="flex" gap={1} alignItems="center">
							<Chip
								label={testingPlanTotal.type}
								color="primary"
								size="small"
							/>
							<Typography variant="body2" color="text.secondary">
								{testingPlanTotal.date_from} to {testingPlanTotal.date_to}
							</Typography>
						</Box>
					</Box>
					<Button
						variant="outlined"
						startIcon={<ArrowBackIcon />}
						onClick={() => navigate("/testing-plan-dashboard")}
					>
						Back to Dashboard
					</Button>
				</Box>
			</Box>

			{/* Progress Summary */}
			{testingPlanTotal.progress_summary && (
				<Paper sx={{ p: 2, mb: 3 }}>
					<Typography variant="h6" gutterBottom>
						Progress Summary
					</Typography>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 8 }}>
							<LinearProgress
								variant="determinate"
								value={
									(testingPlanTotal.progress_summary.completed /
										testingPlanTotal.progress_summary.total) *
									100
								}
								sx={{ height: 10, borderRadius: 5 }}
							/>
							<Typography variant="body2" sx={{ mt: 1 }}>
								{testingPlanTotal.progress_summary.completed} /{" "}
								{testingPlanTotal.progress_summary.total} plans completed
							</Typography>
						</Grid>
						<Grid size={{ xs: 12, md: 4 }}>
							<Box display="flex" gap={2}>
								<Chip
									icon={<ScheduleIcon />}
									label={`Running: ${testingPlanTotal.progress_summary.running}`}
									size="small"
								/>
								<Chip
									icon={<CancelIcon />}
									label={`Failed: ${testingPlanTotal.progress_summary.failed}`}
									size="small"
									color="error"
								/>
							</Box>
						</Grid>
					</Grid>
				</Paper>
			)}

			{/* Tabs */}
			<Paper sx={{ mb: 3 }}>
				<Tabs
					value={tabValue}
					onChange={(_, newValue) => setTabValue(newValue)}
				>
					<Tab label="Testing Plans" icon={<AssignmentIcon />} />
					<Tab label="Information" icon={<TimelineIcon />} />
				</Tabs>
			</Paper>

			{/* Tab Content */}
			{tabValue === 0 && (
				<Box>
					<Box
						display="flex"
						justifyContent="between"
						alignItems="center"
						mb={3}
					>
						{/* <Typography variant="h6">Testing Plans</Typography> */}
						<TextField
							label="Search by plan name or board information"
							variant="outlined"
							size="small"
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							sx={{ flex: 1, mr: 2 }}
							placeholder="Enter plan name or board information name..."
							slotProps={{
								input: {
									startAdornment: (
										<InputAdornment position="start">
											<SearchIcon />
										</InputAdornment>
									),
									endAdornment: searchQuery && (
										<InputAdornment position="end">
											<IconButton
												size="small"
												onClick={() => setSearchQuery("")}
												edge="end"
											>
												<ClearIcon />
											</IconButton>
										</InputAdornment>
									),
								},
							}}
						/>
						<Button
							variant="contained"
							startIcon={<AddIcon />}
							onClick={() => handleOpenPlanDialog()}
						>
							Add Testing Plan
						</Button>
					</Box>

					{/* Search Results Info */}
					{searchQuery.trim() && (
						<Box sx={{ mb: 2 }}>
							<Typography variant="body2" color="text.secondary">
								Found {filteredTestingPlans.length} testing plan(s) matching "
								{searchQuery}"
							</Typography>
						</Box>
					)}

					{/* Testing Plans Accordion */}
					{filteredTestingPlans.length > 0 ? (
						filteredTestingPlans.map((plan) => (
							<Accordion
								key={plan.id}
								expanded={expandedPlan === plan.id}
								onChange={(_, isExpanded) => {
									setExpandedPlan(isExpanded ? plan.id : false);
									if (isExpanded && !planBatches[plan.id]) {
										loadTestingPlanBatches(plan.id);
									}
								}}
								sx={{ mb: 2, position: 'relative' }}
							>
								<AccordionSummary expandIcon={<ExpandMoreIcon />}>
									<Box display="flex" alignItems="center" gap={2} sx={{ width: '100%' }}>
										<Typography variant="h6">
											{highlightSearchTerm(plan.name, searchQuery)}
										</Typography>
										<Chip
											label={"Priority: " + plan.priority}
											color={getPriorityColor(plan.priority)}
											size="small"
										/>
										{plan.board_information_detail && (
											<Chip
												label={
													"Board: " +
													highlightSearchTerm(
														plan.board_information_detail.name,
														searchQuery
													)
												}
												color="info"
												size="small"
												variant="outlined"
											/>
										)}
									</Box>
								</AccordionSummary>

								{/* Action buttons outside AccordionSummary */}
								<Box
									display="flex"
									gap={1}
									sx={{
										position: 'absolute',
										right: 48,
										top: 8,
										zIndex: 1
									}}
								>
									<Button
										size="small"
										startIcon={<PlayIcon />}
										onClick={(e) => {
											e.stopPropagation();
											handleTriggerJenkinsPlan(plan.id, plan.name);
										}}
										color="success"
									>
										Trigger
									</Button>
									<IconButton
										size="small"
										onClick={(e) => {
											e.stopPropagation();
											handleOpenPlanDialog(plan);
										}}
										color="primary"
									>
										<EditIcon />
									</IconButton>
									<IconButton
										size="small"
										onClick={(e) => {
											e.stopPropagation();
											handleDeletePlan(plan.id);
										}}
										color="error"
									>
										<DeleteIcon />
									</IconButton>
								</Box>
								<AccordionDetails>
									<Box>
										{/* Plan Information */}
										{/* <Grid container spacing={2} sx={{ mb: 3 }}>
											<Grid size={{ xs: 3 }}>
												<Typography variant="body2" color="text.secondary">
													<strong>Priority:</strong> {plan.priority}
												</Typography>
											</Grid>
											<Grid size={{ xs: 3 }}>
												<Typography variant="body2" color="text.secondary">
													<strong>Board Information:</strong>{" "}
													{plan.board_information_detail?.name ||
														"Not selected"}{" "}
												</Typography>
											</Grid>
										</Grid> */}

										{/* Show job build */}
										{plan.build_jobs.length > 0 && (
											<Box display="flex" flexDirection="column" mb={2}>
												{plan.build_jobs.map((build_job) => (
													<Box display="flex" key={build_job.id} mb={1}>
														<Chip
															label={
																"Job build: " +
																build_job.jenkins_build_number +
																" - " +
																convertDate(build_job.completed_at) +
																" - " +
																build_job.jenkins_status
															}
															color={getBuildColor(build_job.jenkins_status)}
															variant="outlined"
															onClick={() =>
																handleNavigateJenkins(build_job.jenkins_url)
															}
														/>
													</Box>
												))}
											</Box>
										)}

										{/* Add Batch Button */}
										<Box
											display="flex"
											justifyContent="space-between"
											alignItems="center"
											mb={2}
										>
											<Typography variant="subtitle1" fontWeight="bold">
												Testing Plan Batches
											</Typography>
											<Button
												variant="outlined"
												size="small"
												startIcon={<AddIcon />}
												onClick={() => {
													setSelectedPlan(plan);
													setEditingBatch(null);
													setBatchFormData({
														name: "",
														status: "Pending",
														order: 1,
														test_cases: [],
														selected_modules: [],
														selected_suites: [],
													});
													setBatchDialogOpen(true);
												}}
											>
												Add Batch
											</Button>
										</Box>

										{/* Testing Plan Batches Accordion */}
										{planBatches[plan.id]?.map((batch) => (
											<Accordion
												key={batch.id}
												expanded={expandedBatch[batch.id] || false}
												onChange={(_, isExpanded) => {
													setExpandedBatch((prev) => ({
														...prev,
														[batch.id]: isExpanded,
													}));
													if (isExpanded) {
														if (!batchRuns[batch.id]) {
															loadTestingPlanRuns(batch.id);
														}
														if (!batchResults[batch.id]) {
															loadBatchResults(batch.id);
														}
													}
												}}
												sx={{ mb: 1, ml: 2, position: 'relative' }}
											>
												<AccordionSummary expandIcon={<ExpandMoreIcon />}>
													<Box display="flex" alignItems="center" gap={2} sx={{ width: '100%' }}>
														<Typography variant="subtitle1">
															{batch.name}
														</Typography>
														<Chip
															label={batch.status}
															color={getStatusColor(batch.status)}
															size="small"
														/>
														{(() => {
															const stats = calculateBatchStats(batch);
															return (
																<Box display="flex" gap={0.5}>
																	<Chip
																		label={`${stats.pass}P`}
																		color="success"
																		size="small"
																	/>
																	<Chip
																		label={`${stats.fail}F`}
																		color="error"
																		size="small"
																	/>
																	<Chip
																		label={`${stats.skip}S`}
																		color="warning"
																		size="small"
																	/>
																	<Chip
																		label={`${stats.notTested}NT`}
																		color="default"
																		size="small"
																	/>
																</Box>
															);
														})()}
													</Box>
												</AccordionSummary>

												{/* Action buttons outside AccordionSummary */}
												<Box
													display="flex"
													gap={1}
													sx={{
														position: 'absolute',
														right: 48,
														top: 8,
														zIndex: 1
													}}
												>
													<IconButton
														size="small"
														onClick={(e) => {
															e.stopPropagation();
															setSelectedPlan(plan);
															setEditingBatch(batch);
															setBatchFormData({
																name: batch.name,
																status: batch.status || "Pending",
																order: batch.order,
																test_cases: batch.test_cases || [],
																selected_modules: batch.selected_modules || [],
																selected_suites: batch.selected_suites || [],
															});
															setBatchDialogOpen(true);
														}}
														color="primary"
													>
														<EditIcon />
													</IconButton>
													<IconButton
														size="small"
														onClick={(e) => {
															e.stopPropagation();
															handleDeleteBatch(plan.id, batch.id);
														}}
														color="error"
													>
														<DeleteIcon />
													</IconButton>
												</Box>
												<AccordionDetails>
													<Box>
														{/* Test Cases and Results */}
														<Typography variant="subtitle2" gutterBottom>
															Test Cases & Results:
														</Typography>
														{(() => {
															const stats = calculateBatchStats(batch);
															return (
																<Box sx={{ mb: 2 }}>
																	<Box display="flex" gap={1} sx={{ mb: 2 }}>
																		<Chip
																			label={`Total: ${stats.total}`}
																			size="small"
																		/>
																		<Chip
																			label={`Pass: ${stats.pass}`}
																			color="success"
																			size="small"
																		/>
																		<Chip
																			label={`Fail: ${stats.fail}`}
																			color="error"
																			size="small"
																		/>
																		<Chip
																			label={`Skip: ${stats.skip}`}
																			color="warning"
																			size="small"
																		/>
																		<Chip
																			label={`Not Tested: ${stats.notTested}`}
																			color="default"
																			size="small"
																		/>
																	</Box>

																	{/* {batch.test_cases_detail &&
																	batch.test_cases_detail.length > 0 ? (
																		<TableContainer
																			component={Paper}
																			variant="outlined"
																			sx={{ mb: 2 }}
																		>
																			<Table size="small">
																				<TableHead>
																					<TableRow>
																						<TableCell>Test Case ID</TableCell>
																						<TableCell>Brief</TableCell>
																						<TableCell>Latest Result</TableCell>
																						<TableCell>Run Count</TableCell>
																					</TableRow>
																				</TableHead>
																				<TableBody>
																					{batch.test_cases_detail.map(
																						(testCase) => {
																							const testCaseResults =
																								batchResults[batch.id]?.filter(
																									(r) =>
																										r.test_case === testCase.id
																								) || [];
																							const latestResult =
																								testCaseResults.length > 0
																									? testCaseResults[
																											testCaseResults.length - 1
																									  ]
																									: null;

																							return (
																								<TableRow key={testCase.id}>
																									<TableCell>
																										{testCase.testcase_id}
																									</TableCell>
																									<TableCell>
																										{testCase.brief}
																									</TableCell>
																									<TableCell>
																										{latestResult ? (
																											<Chip
																												label={
																													latestResult.result
																												}
																												color={getResultColor(
																													latestResult.result
																												)}
																												size="small"
																											/>
																										) : (
																											<Chip
																												label="Not Tested"
																												color="default"
																												size="small"
																											/>
																										)}
																									</TableCell>
																									<TableCell>
																										{testCaseResults.length}
																									</TableCell>
																								</TableRow>
																							);
																						}
																					)}
																				</TableBody>
																			</Table>
																		</TableContainer>
																	) : (
																		<Typography
																			variant="body2"
																			color="text.secondary"
																			sx={{ mb: 2 }}
																		>
																			No test cases assigned to this batch.
																		</Typography>
																	)} */}
																</Box>
															);
														})()}

														{/* Job Runs */}
														<Typography variant="subtitle2" gutterBottom>
															Job Runs:
														</Typography>
														{batchRuns[batch.id]?.length > 0 ? (
															<TableContainer
																component={Paper}
																variant="outlined"
															>
																<Table size="small">
																	<TableHead>
																		<TableRow>
																			<TableCell>#</TableCell>
																			<TableCell>Build #</TableCell>
																			<TableCell>Status</TableCell>
																			<TableCell>JenkinsURL</TableCell>
																			{/* <TableCell>RC Build</TableCell> */}
																			{/* <TableCell>Results</TableCell> */}
																			<TableCell>Completed At</TableCell>
																		</TableRow>
																	</TableHead>
																	<TableBody>
																		{batchRuns[batch.id].map((run, index) => (
																			<TableRow key={run.id}>
																				<TableCell>{index}</TableCell>
																				<TableCell>
																					{run.jenkins_build_number || "-"}
																				</TableCell>
																				<TableCell>
																					<Chip
																						label={run.status}
																						color={getStatusColor(run.status)}
																						size="small"
																					/>
																				</TableCell>
																				<TableCell>
																					<Link
																						href={run.jenkins_url}
																						target="_blank"
																					>
																						{run.jenkins_url}
																					</Link>
																				</TableCell>
																				{/* <TableCell>
																					{run.jenkins_job_name || "-"}
																				</TableCell>
																				<TableCell>
																					{run.rc_build || "-"}
																				</TableCell> */}
																				{/* <TableCell>4/3/1</TableCell> */}
																				<TableCell>
																					{new Date(
																						run.completed_at
																					).toLocaleDateString()}
																				</TableCell>
																			</TableRow>
																		))}
																	</TableBody>
																</Table>
															</TableContainer>
														) : (
															<Typography
																variant="body2"
																color="text.secondary"
																sx={{ fontStyle: "italic" }}
															>
																No job runs yet
															</Typography>
														)}
													</Box>
												</AccordionDetails>
											</Accordion>
										))}

										{loadingBatches[plan.id] ? (
											<Box sx={{ display: 'flex', alignItems: 'center', ml: 2, py: 2 }}>
												<CircularProgress size={20} sx={{ mr: 1 }} />
												<Typography
													variant="body2"
													color="text.secondary"
													sx={{ fontStyle: "italic" }}
												>
													Loading batches...
												</Typography>
											</Box>
										) : (!planBatches[plan.id] ||
											planBatches[plan.id].length === 0) && (
											<Typography
												variant="body2"
												color="text.secondary"
												sx={{ fontStyle: "italic", ml: 2 }}
											>
												No batches created yet
											</Typography>
										)}
									</Box>
								</AccordionDetails>
							</Accordion>
						))
					) : (
						<Typography
							variant="body1"
							color="text.secondary"
							sx={{ textAlign: "center", py: 4 }}
						>
							{searchQuery.trim()
								? `No testing plans found matching "${searchQuery}"`
								: "No testing plans created yet"}
						</Typography>
					)}
				</Box>
			)}

			{tabValue === 1 && (
				<Paper sx={{ p: 3 }}>
					<Typography variant="h6" gutterBottom>
						Testing Plan Total Information
					</Typography>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6 }}>
							<Typography variant="subtitle2" gutterBottom>
								Information:
							</Typography>
							<Typography variant="body2" sx={{ mb: 2 }}>
								{testingPlanTotal.information || "No information provided"}
							</Typography>
						</Grid>
						<Grid size={{ xs: 12, md: 6 }}>
							<Typography variant="subtitle2" gutterBottom>
								Description:
							</Typography>
							<Typography
								variant="body2"
								component="pre"
								sx={{ mb: 2, fontSize: "0.875rem" }}
							>
								{typeof testingPlanTotal.description === "object"
									? JSON.stringify(testingPlanTotal.description, null, 2)
									: testingPlanTotal.description || "No description"}
							</Typography>
						</Grid>
						<Grid size={{ xs: 12 }}>
							<Typography variant="subtitle2" gutterBottom>
								Common Configuration:
							</Typography>
							<Typography
								variant="body2"
								component="pre"
								sx={{ fontSize: "0.875rem" }}
							>
								{typeof testingPlanTotal.common === "object"
									? JSON.stringify(testingPlanTotal.common, null, 2)
									: testingPlanTotal.common || "No common configuration"}
							</Typography>
						</Grid>
					</Grid>
				</Paper>
			)}

			{/* Testing Plan Dialog */}
			<Dialog
				open={planDialogOpen}
				onClose={() => setPlanDialogOpen(false)}
				maxWidth="md"
				fullWidth
			>
				<DialogTitle>
					{editingPlan ? "Edit Testing Plan" : "Create New Testing Plan"}
				</DialogTitle>
				<DialogContent>
					<Grid container spacing={2} sx={{ mb: 2 }}>
						<Grid size={{ xs: 6 }}>
							{/* <FormControl fullWidth variant="outlined">
								<InputLabel>Status</InputLabel>
								<Select
									value={planFormData.status}
									onChange={(e) => setPlanFormData({ ...planFormData, status: e.target.value })}
									label="Status"
								>
									{choices.testing_plan_status_choices?.map(([value, label]) => (
										<MenuItem key={value} value={value}>{label}</MenuItem>
									))}
								</Select>
							</FormControl> */}
							<TextField
								autoFocus
								// margin="dense"
								label="Name"
								fullWidth
								variant="outlined"
								value={planFormData.name}
								onChange={(e) =>
									setPlanFormData({ ...planFormData, name: e.target.value })
								}
								sx={{ mt: 1 }}
								required
							/>
						</Grid>
						<Grid size={{ xs: 6 }}>
							<TextField
								sx={{ mt: 1 }}
								label="Priority"
								type="number"
								fullWidth
								variant="outlined"
								value={planFormData.priority}
								onChange={(e) =>
									setPlanFormData({
										...planFormData,
										priority: parseInt(e.target.value) || 100,
									})
								}
							// helperText="Priority as integer (default: 100)"
							/>
						</Grid>
					</Grid>

					<TextField
						margin="dense"
						label="Description (JSON format)"
						fullWidth
						multiline
						rows={3}
						variant="outlined"
						value={planFormData.description}
						onChange={(e) =>
							setPlanFormData({ ...planFormData, description: e.target.value })
						}
						sx={{ mb: 2 }}
						helperText="Enter valid JSON format"
					/>

					<Grid container spacing={2} sx={{ mb: 2 }}>
						<Grid size={{ xs: 12 }}>
							<FormControl fullWidth variant="outlined">
								<InputLabel>Board Information</InputLabel>
								<Select
									value={planFormData.board_information}
									onChange={(e) =>
										setPlanFormData({
											...planFormData,
											board_information: e.target.value,
										})
									}
									label="Board Information"
								>
									{boardInformation.map((board) => (
										<MenuItem key={board.id} value={board.id}>
											{board.name}
										</MenuItem>
									))}
								</Select>
							</FormControl>
						</Grid>
						{/* <Grid size={{ xs: 6 }}>
							<Autocomplete
								multiple
								// limitTags={20}
								sx={{
									"& .MuiAutocomplete-inputRoot": {
										maxHeight: 300,
										overflowY: "auto",
										flexWrap: "wrap",
									},
								}}
								options={testCaseOptions}
								value={testCaseOptions.filter(
									(option) =>
										option.type === "testcase" &&
										planFormData.test_cases.includes(option.id)
								)}
								onChange={(event, newValue) => {
									// Handle suite selection/deselection
									const lastSelected = newValue[newValue.length - 1];

									if (lastSelected && lastSelected.type === "suite") {
										// If a suite is selected, toggle all test cases in that suite
										const suiteTestCases = lastSelected.testCases.map(
											(tc) => tc.id
										);
										const currentSelected = planFormData.test_cases;

										// Check if all test cases in suite are already selected
										const allSelected = suiteTestCases.every((id) =>
											currentSelected.includes(id)
										);

										let updatedSelection;
										if (allSelected) {
											// Deselect all test cases in suite
											updatedSelection = currentSelected.filter(
												(id) => !suiteTestCases.includes(id)
											);
										} else {
											// Select all test cases in suite
											updatedSelection = [
												...new Set([...currentSelected, ...suiteTestCases]),
											];
										}

										setPlanFormData({
											...planFormData,
											test_cases: updatedSelection,
										});
									} else {
										// Handle individual test case selection
										const selectedIds = newValue
											.filter((option) => option.type === "testcase")
											.map((option) => option.id);
										setPlanFormData({
											...planFormData,
											test_cases: selectedIds,
										});
									}
								}}
								getOptionLabel={(option) => option.label}
								groupBy={(option) =>
									option.type === "suite" ? "Suites" : option.suite
								}
								renderOption={(props, option) => {
									if (option.type === "suite") {
										// Render suite option with checkbox
										const suiteTestCases = option.testCases.map((tc) => tc.id);
										const allSelected = suiteTestCases.every((id) =>
											planFormData.test_cases.includes(id)
										);
										const someSelected = suiteTestCases.some((id) =>
											planFormData.test_cases.includes(id)
										);

										return (
											<li {...props}>
												<Checkbox
													checked={allSelected}
													indeterminate={someSelected && !allSelected}
													style={{ marginRight: 8 }}
												/>
												<ListItemText
													primary={option.label}
													secondary={`${option.testCases.length} test cases`}
												/>
											</li>
										);
									} else {
										// Render test case option with checkbox
										const isSelected = planFormData.test_cases.includes(
											option.id
										);
										return (
											<li {...props}>
												<Checkbox
													checked={isSelected}
													style={{ marginRight: 8 }}
												/>
												<ListItemText
													primary={option.label}
													secondary={option.testCase.brief || "No description"}
												/>
											</li>
										);
									}
								}}
								renderInput={(params) => (
									<TextField
										{...params}
										label="Test Cases"
										placeholder="Select test cases by suite or individually..."
										variant="outlined"
										maxRows={4}
										sx={{ maxHeight: "400px" }}
									/>
								)}
								disableCloseOnSelect
								isOptionEqualToValue={(option, value) => option.id === value.id}
							/>
						</Grid> */}
					</Grid>
				</DialogContent>
				<DialogActions>
					<Button onClick={() => setPlanDialogOpen(false)}>Cancel</Button>
					<Button
						onClick={handleSubmitPlan}
						variant="contained"
						disabled={!planFormData.name.trim()}
					>
						{editingPlan ? "Update" : "Create"}
					</Button>
				</DialogActions>
			</Dialog>

			{/* Testing Plan Batch Dialog */}
			<Dialog
				open={batchDialogOpen}
				onClose={() => setBatchDialogOpen(false)}
				maxWidth="md"
				fullWidth
			>
				<DialogTitle>
					{editingBatch
						? "Edit Testing Plan Batch"
						: "Create New Testing Plan Batch"}
				</DialogTitle>
				<DialogContent>
					<TextField
						autoFocus
						margin="dense"
						label="Batch Name"
						fullWidth
						variant="outlined"
						value={batchFormData.name}
						onChange={(e) =>
							setBatchFormData({ ...batchFormData, name: e.target.value })
						}
						sx={{ mb: 2 }}
						required
					/>

					{/* <TextField
						margin="dense"
						label="Order"
						type="number"
						fullWidth
						variant="outlined"
						value={batchFormData.order}
						onChange={(e) => setBatchFormData({ ...batchFormData, order: parseInt(e.target.value) || 1 })}
						sx={{ mb: 2 }}
						slotProps={{ htmlInput: { min: 1 } }}
					/> */}

					{/* <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
						<InputLabel>Status</InputLabel>
						<Select
							value={batchFormData.status}
							onChange={(e) => setBatchFormData({ ...batchFormData, status: e.target.value })}
							label="Status"
						>
							<MenuItem value="Pending">Pending</MenuItem>
							<MenuItem value="Running">Running</MenuItem>
							<MenuItem value="Completed">Completed</MenuItem>
							<MenuItem value="Failed">Failed</MenuItem>
							<MenuItem value="Skipped">Skipped</MenuItem>
						</Select>
					</FormControl> */}
					<Autocomplete
						multiple
						disabled={loadingExistingTestCases}
						options={dynamicModuleOptions}
						value={selectedModules}
						onChange={(_, newValue) => {
							// Check if a module was removed
							const removedModules = selectedModules.filter(
								oldModule => !newValue.some(newModule => newModule.value === oldModule.value)
							);

							// Handle removed modules
							if (removedModules.length > 0) {
								removedModules.forEach(removedModule => {
									console.log("Module removed via onChange:", removedModule.value);
									handleRemoveModule(removedModule.value);
								});
							}

							// Handle added modules
							const addedModules = newValue.filter(
								newModule => !selectedModules.some(oldModule => oldModule.value === newModule.value)
							);

							if (addedModules.length > 0) {
								console.log("Modules added:", addedModules.map(m => m.value));
								handleModuleSelection(addedModules.map(item => item.value));
							}

							setSelectedModules(newValue);
						}}
						onInputChange={(_, newInputValue) => {
							setModuleSearchTerm(newInputValue);
							searchModules(newInputValue);
						}}
						inputValue={moduleSearchTerm}
						getOptionLabel={(option) => option.label || option}
						isOptionEqualToValue={(option, value) => option.value === value.value}
						sx={{
							"& .MuiAutocomplete-inputRoot": {
								maxHeight: 300,
								overflowY: "auto",
								flexWrap: "wrap",
							},
							mb: 2,
						}}

						renderInput={(params) => (
							<TextField
								{...params}
								label="Module"
								placeholder="Type to search modules..."
								variant="outlined"
								helperText={`${selectedModules.length} module(s) selected`}
							/>
						)}
						disableCloseOnSelect
					/>
					<Autocomplete
						multiple
						disabled={loadingExistingTestCases}
						options={dynamicSuiteOptions}
						value={selectedSuites}
						onChange={(_, newValue) => {
							// Check if a suite was removed
							const removedSuites = selectedSuites.filter(
								oldSuite => !newValue.some(newSuite => newSuite.value === oldSuite.value)
							);

							// Handle removed suites
							if (removedSuites.length > 0) {
								removedSuites.forEach(removedSuite => {
									console.log("Suite removed via onChange:", removedSuite.value);
									handleRemoveSuite(removedSuite.value);
								});
							}

							// Handle added suites
							const addedSuites = newValue.filter(
								newSuite => !selectedSuites.some(oldSuite => oldSuite.value === newSuite.value)
							);

							if (addedSuites.length > 0) {
								console.log("Suites added:", addedSuites.map(s => s.value));
								handleSuiteSelection(addedSuites.map(item => item.value));
							}

							setSelectedSuites(newValue);
						}}
						onInputChange={(_, newInputValue) => {
							setSuiteSearchTerm(newInputValue);
							searchSuites(newInputValue);
						}}
						inputValue={suiteSearchTerm}
						getOptionLabel={(option) => option.label || option}
						isOptionEqualToValue={(option, value) => option.value === value.value}
						sx={{
							"& .MuiAutocomplete-inputRoot": {
								maxHeight: 300,
								overflowY: "auto",
								flexWrap: "wrap",
							},
							mb: 2,
						}}

						renderInput={(params) => (
							<TextField
								{...params}
								label="Suite"
								placeholder="Type to search suites..."
								variant="outlined"
								helperText={`${selectedSuites.length} suite(s) selected`}
							/>
						)}
						disableCloseOnSelect
					/>
					<Autocomplete
						multiple
						disabled={loadingExistingTestCases}
						options={dynamicTestCaseOptions}
						loading={loadingExistingTestCases}
						value={dynamicTestCaseOptions.filter(option =>
							batchFormData.test_cases.includes(option.value)
						)}
						onChange={(_, newValue) => {
							const selectedIds = newValue.map(option => option.value);
							setBatchFormData(prev => ({ ...prev, test_cases: selectedIds }));
						}}
						onInputChange={(_, newInputValue) => {
							setTestCaseSearchTerm(newInputValue);
							searchTestCases(newInputValue);
						}}
						inputValue={testCaseSearchTerm}
						getOptionLabel={(option) => option.label || option}
						isOptionEqualToValue={(option, value) => option.value === value.value}
						sx={{
							"& .MuiAutocomplete-inputRoot": {
								maxHeight: 300,
								overflowY: "auto",
								flexWrap: "wrap",
							},
							mb: 2,
						}}
						renderOption={(props, option) => {
							const isSelected = batchFormData.test_cases.includes(option.value);
							return (
								<li {...props}>
									<Checkbox checked={isSelected} style={{ marginRight: 8 }} />
									<ListItemText
										primary={option.label}
										secondary={option.testCase?.brief || "No description"}
									/>
								</li>
							);
						}}
						renderInput={(params) => (
							<TextField
								{...params}
								label="Test Cases"
								placeholder={loadingExistingTestCases ? "Loading existing test cases..." : "Type to search test cases..."}
								variant="outlined"
								slotProps={{
									input: {
										...params.InputProps,
										endAdornment: (
											<>
												{loadingExistingTestCases ? <CircularProgress color="inherit" size={20} /> : null}
												{params.InputProps.endAdornment}
											</>
										),
									},
								}}
							/>
						)}
						disableCloseOnSelect
					/>
				</DialogContent>
				<DialogActions>
					<Button onClick={() => setBatchDialogOpen(false)}>Cancel</Button>
					<Button
						onClick={handleSubmitBatch}
						variant="contained"
						disabled={!batchFormData.name.trim()}
					>
						{editingBatch ? "Update" : "Create"}
					</Button>
				</DialogActions>
			</Dialog>

			<Snackbar
				open={snackbar.open}
				autoHideDuration={6000}
				onClose={() => setSnackbar({ ...snackbar, open: false })}
			>
				<Alert
					onClose={() => setSnackbar({ ...snackbar, open: false })}
					severity={snackbar.severity}
					sx={{ width: "100%" }}
				>
					{snackbar.message}
				</Alert>
			</Snackbar>
		</Box>
	);
};

export default TestingPlanDetail;
