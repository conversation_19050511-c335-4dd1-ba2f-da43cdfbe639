import { useState, useEffect } from 'react';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	Button,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	IconButton,
	Chip,
	Alert,
	Snackbar,
	FormControl,
	InputLabel,
	Select,
	MenuItem,
	OutlinedInput,
	Checkbox,
	ListItemText,
	CircularProgress,
} from '@mui/material';
import {
	Add as AddIcon,
	Edit as EditIcon,
	Delete as DeleteIcon,
	AccountTree as TopologyIcon,
} from '@mui/icons-material';
import { boardService } from '../services';

const BoardTopology = () => {
	const [boardTopologies, setBoardTopologies] = useState([]);
	const [boardInfos, setBoardInfos] = useState([]);
	const [loading, setLoading] = useState(false);
	const [open, setOpen] = useState(false);
	const [editingTopology, setEditingTopology] = useState(null);
	const [formData, setFormData] = useState({
		name: '',
		board_information: [],
		description: {},
	});
	const [snackbar, setSnackbar] = useState({
		open: false,
		message: '',
		severity: 'success',
	});

	useEffect(() => {
		loadBoardTopologies();
		loadBoardInfos();
	}, []);

	const loadBoardTopologies = async () => {
		try {
			setLoading(true);
			const data = await boardService.getBoardTopology();
			setBoardTopologies(data.results || data);
		} catch (error) {
			showSnackbar('Error loading board topologies: ' + error.message, 'error');
		} finally {
			setLoading(false);
		}
	};

	const loadBoardInfos = async () => {
		try {
			const data = await boardService.getBoardInformation();
			setBoardInfos(data.results || data);
		} catch (error) {
			console.error('Error loading board information:', error);
		}
	};

	const showSnackbar = (message, severity = 'success') => {
		setSnackbar({ open: true, message, severity });
	};

	const handleOpen = (topology = null) => {
		if (topology) {
			setEditingTopology(topology);
			setFormData({
				name: topology.name,
				board_information: topology.board_information || [],
				description: topology.description || {},
			});
		} else {
			setEditingTopology(null);
			setFormData({
				name: '',
				board_information: [],
				description: {},
			});
		}
		setOpen(true);
	};

	const handleClose = () => {
		setOpen(false);
		setEditingTopology(null);
		setFormData({
			name: '',
			board_information: [],
			description: {},
		});
	};

	const handleSubmit = async () => {
		if (!formData.name.trim() || formData.board_information.length === 0) {
			showSnackbar('Topology name and at least one Board Information are required', 'error');
			return;
		}

		if (typeof formData.description === 'string') {
			try {
				JSON.parse(formData.description);
			} catch (error) {
				showSnackbar('Description must be valid JSON format', 'error');
				return;
			}
		}

		try {
			setLoading(true);
			console.log('Creating topology:', formData);
			const topologyData = {
				name: formData.name.trim(),
				board_information: formData.board_information,
				description: formData.description,
			};

			if (editingTopology) {
				await boardService.updateBoardTopology(editingTopology.id, topologyData);
				showSnackbar('Board topology updated successfully');
			} else {
				await boardService.createBoardTopology(topologyData);
				showSnackbar('Board topology created successfully');
			}

			loadBoardTopologies();
			handleClose();
		} catch (error) {
			showSnackbar('Error saving board topology: ' + error.message, 'error');
		} finally {
			setLoading(false);
		}
	};

	const handleDelete = async (id) => {
		if (window.confirm('Are you sure you want to delete this board topology?')) {
			try {
				setLoading(true);
				await boardService.deleteBoardTopology(id);
				showSnackbar('Board topology deleted successfully');
				loadBoardTopologies();
			} catch (error) {
				showSnackbar('Error deleting board topology: ' + error.message, 'error');
			} finally {
				setLoading(false);
			}
		}
	};



	return (
		<Box sx={{ p: 3}}>
			<Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
				<Typography variant="h4">
					Board Topology
				</Typography>
				<Button
					variant="contained"
					startIcon={<AddIcon />}
					onClick={() => handleOpen()}
					disabled={boardInfos.length === 0}
				>
					Add Board Topology
				</Button>
			</Box>

			{loading && (
				<Box display="flex" justifyContent="center" my={2}>
					<CircularProgress />
				</Box>
			)}

			{boardInfos.length === 0 && (
				<Alert severity="warning" sx={{ mb: 3 }}>
					Please create Board Information first before adding Board Topology.
				</Alert>
			)}

			<TableContainer component={Paper}>
				<Table>
					<TableHead>
						<TableRow>
							<TableCell>Topology Name</TableCell>
							<TableCell>Board Information</TableCell>
							<TableCell>Description</TableCell>
							<TableCell align="center">Actions</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{boardTopologies.length === 0 ? (
							<TableRow>
								<TableCell colSpan={4} align="center">
									<Box py={4}>
										<TopologyIcon color="disabled" sx={{ fontSize: 48, mb: 2 }} />
										<Typography variant="body1" color="textSecondary">
											No board topology found. Create your first board topology.
										</Typography>
									</Box>
								</TableCell>
							</TableRow>
						) : (
							boardTopologies.map((topology) => (
								<TableRow key={topology.id}>
									<TableCell>
										<Chip
											label={topology.name}
											color="success"
											variant="outlined"
										/>
									</TableCell>
									<TableCell>
										<Typography variant="body2">
											{topology.board_information_names || 'No boards selected'}
										</Typography>
									</TableCell>
									<TableCell>
										<Typography variant="body2" sx={{ maxWidth: 300 }}>
											{typeof topology.description === 'object'
												? JSON.stringify(topology.description, null, 2)
												: topology.description || 'No description'}
										</Typography>
									</TableCell>
									<TableCell align="center">
										<IconButton
											size="small"
											onClick={() => handleOpen(topology)}
											color="primary"
										>
											<EditIcon />
										</IconButton>
										<IconButton
											size="small"
											onClick={() => handleDelete(topology.id)}
											color="error"
										>
											<DeleteIcon />
										</IconButton>
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</TableContainer>

			<Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
				<DialogTitle>
					{editingTopology ? 'Edit Board Topology' : 'Add New Board Topology'}
				</DialogTitle>
				<DialogContent>
					<TextField
						autoFocus
						margin="dense"
						label="Topology Name"
						fullWidth
						variant="outlined"
						value={formData.name}
						onChange={(e) => setFormData({ ...formData, name: e.target.value })}
						sx={{ mb: 2 }}
					/>

					<FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
						<InputLabel>Board Information</InputLabel>
						<Select
							multiple
							value={formData.board_information}
							onChange={(e) => setFormData({ ...formData, board_information: e.target.value })}
							input={<OutlinedInput label="Board Information" />}
							renderValue={(selected) => {
								return selected.map(id => {
									const boardInfo = boardInfos.find(b => b.id === id);
									return boardInfo ? boardInfo.name : 'Unknown';
								}).join(', ');
							}}
						>
							{boardInfos.map((boardInfo) => (
								<MenuItem key={boardInfo.id} value={boardInfo.id}>
									<Checkbox checked={formData.board_information.indexOf(boardInfo.id) > -1} />
									<ListItemText primary={boardInfo.name} />
								</MenuItem>
							))}
						</Select>
					</FormControl>

					<TextField
						margin="dense"
						label="Description (JSON Format)"
						fullWidth
						multiline
						rows={6}
						variant="outlined"
						value={typeof formData.description === 'object'
							? JSON.stringify(formData.description, null, 2)
							: formData.description}
						onChange={(e) => {
							try {
								const parsed = JSON.parse(e.target.value);
								setFormData({ ...formData, description: parsed });
							} catch {
								setFormData({ ...formData, description: e.target.value });
							}
						}}
						placeholder='{"type": "mesh", "connections": ["ethernet", "usb"], "power": "shared"}'
						sx={{ mb: 2 }}
					/>
				</DialogContent>
				<DialogActions>
					<Button onClick={handleClose}>Cancel</Button>
					<Button
						onClick={handleSubmit}
						variant="contained"
						disabled={!formData.name.trim() || formData.board_information.length === 0 || formData.description.length === 0}
					>
						{editingTopology ? 'Update' : 'Create'}
					</Button>
				</DialogActions>
			</Dialog>

			<Snackbar
				open={snackbar.open}
				autoHideDuration={6000}
				onClose={() => setSnackbar({ ...snackbar, open: false })}
			>
				<Alert
					onClose={() => setSnackbar({ ...snackbar, open: false })}
					severity={snackbar.severity}
					sx={{ width: '100%' }}
				>
					{snackbar.message}
				</Alert>
			</Snackbar>
		</Box >
	);
};

export default BoardTopology;
