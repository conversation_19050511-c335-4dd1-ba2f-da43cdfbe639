import React from 'react';
import {
  Typo<PERSON>,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardActions,
  Button,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Computer as ComputerIcon,
  Build as BuildIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { useLocalStorage } from '../hooks/useLocalStorage';
import { usePermissions } from '../hooks/usePermissions';
import { testcaseService } from '../services';

const Dashboard = () => {
  const [testCases] = useLocalStorage('testCases', []);
  const [boards] = useLocalStorage('boards', []);
  const [buildConfigs] = useLocalStorage('buildConfigs', []);
  const [users] = useLocalStorage('users', []);
  const { user, canManageUsers } = usePermissions();

  const stats = [
    { title: 'Total Test Cases', value: testCases.length.toString(), icon: <AssignmentIcon />, color: '#1976d2' },
    { title: 'Board Configurations', value: boards.length.toString(), icon: <ComputerIcon />, color: '#388e3c' },
    { title: 'Build Configurations', value: buildConfigs.length.toString(), icon: <BuildIcon />, color: '#f57c00' },
    ...(canManageUsers() ? [{ title: 'Total Users', value: users.length.toString(), icon: <PeopleIcon />, color: '#7b1fa2' }] : []),
  ];

  return (
    <Box sx={{p: 3}}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Dashboard
        </Typography>
        <Box>
          <Typography variant="h6" color="textSecondary">
            Welcome back, {user?.fullName || user?.username}!
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Role: {user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1)}
          </Typography>
        </Box>
      </Box>
      <Typography variant="body1" color="textSecondary" gutterBottom>
        Welcome to the Testing Dashboard. Manage your test cases, board configurations, and trigger Yocto builds.
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <Box
                    sx={{
                      backgroundColor: stat.color,
                      color: 'white',
                      borderRadius: '50%',
                      width: 40,
                      height: 40,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Box>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activities
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • Build configuration "ARM64-Debug" created
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • Test case "Network Connectivity" updated
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • Board "RaspberryPi4" configuration modified
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • Jenkins build #156 completed successfully
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Box display="flex" flexDirection="column" gap={2}>
              <Button variant="contained" startIcon={<AssignmentIcon />}>
                Create New Test Case
              </Button>
              <Button variant="contained" startIcon={<ComputerIcon />}>
                Add Board Configuration
              </Button>
              <Button variant="contained" startIcon={<BuildIcon />}>
                Create Build Configuration
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
