import { useState, useEffect } from 'react';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	Button,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	IconButton,
	Chip,
	Alert,
	Snackbar,
	FormControl,
	InputLabel,
	Select,
	MenuItem,
	CircularProgress,
} from '@mui/material';
import {
	Add as AddIcon,
	Edit as EditIcon,
	Delete as DeleteIcon,
	Memory as MemoryIcon,
} from '@mui/icons-material';
import { boardService } from '../services';

const BoardHW = () => {
	const [boardHWs, setBoardHWs] = useState([]);
	const [boardInfos, setBoardInfos] = useState([]);
	const [loading, setLoading] = useState(false);
	const [open, setOpen] = useState(false);
	const [editingBoardHW, setEditingBoardHW] = useState(null);
	const [formData, setFormData] = useState({
		name: '',
		board_information: '',
		description: {},
	});
	const [snackbar, setSnackbar] = useState({
		open: false,
		message: '',
		severity: 'success',
	});

	useEffect(() => {
		loadBoardHWs();
		loadBoardInfos();
	}, []);

	const loadBoardHWs = async () => {
		try {
			setLoading(true);
			const data = await boardService.getBoardHW();
			setBoardHWs(data.results || data);
		} catch (error) {
			showSnackbar('Error loading board hardware: ' + error.message, 'error');
		} finally {
			setLoading(false);
		}
	};

	const loadBoardInfos = async () => {
		try {
			const data = await boardService.getBoardInformation();
			setBoardInfos(data.results || data);
		} catch (error) {
			console.error('Error loading board information:', error);
		}
	};

	const showSnackbar = (message, severity = 'success') => {
		setSnackbar({ open: true, message, severity });
	};

	const handleOpen = (boardHW = null) => {
		if (boardHW) {
			setEditingBoardHW(boardHW);
			setFormData({
				name: boardHW.name,
				board_information: boardHW.board_information,
				description: boardHW.description || {},
			});
		} else {
			setEditingBoardHW(null);
			setFormData({
				name: '',
				board_information: '',
				description: {},
			});
		}
		setOpen(true);
	};

	const handleClose = () => {
		setOpen(false);
		setEditingBoardHW(null);
		setFormData({
			name: '',
			board_information: '',
			description: '',
			cpu: '',
			memory: '',
			storage: '',
			network: '',
			other_specs: '',
		});
	};

	const handleSubmit = async () => {
		if (!formData.name.trim() || !formData.board_information) {
			showSnackbar('Board HW name and Board Information are required', 'error');
			return;
		}

		if (typeof formData.description === 'string') {
			try {
				JSON.parse(formData.description);
			} catch (error) {
				showSnackbar('Description must be valid JSON format', 'error');
				return;
			}
		}

		try {
			setLoading(true);
			const boardHWData = {
				name: formData.name.trim(),
				board_information: formData.board_information,
				description: formData.description,
			};

			if (editingBoardHW) {
				await boardService.updateBoardHW(editingBoardHW.id, boardHWData);
				showSnackbar('Board HW updated successfully');
			} else {
				await boardService.createBoardHW(boardHWData);
				showSnackbar('Board HW created successfully');
			}

			loadBoardHWs();
			handleClose();
		} catch (error) {
			showSnackbar('Error saving board HW: ' + error.message, 'error');
		} finally {
			setLoading(false);
		}
	};

	const handleDelete = async (id) => {
		if (window.confirm('Are you sure you want to delete this board HW?')) {
			try {
				setLoading(true);
				await boardService.deleteBoardHW(id);
				showSnackbar('Board HW deleted successfully');
				loadBoardHWs();
			} catch (error) {
				showSnackbar('Error deleting board HW: ' + error.message, 'error');
			} finally {
				setLoading(false);
			}
		}
	};

	const getBoardInformationName = (boardInformationId) => {
		const boardInfo = boardInfos.find(b => b.id === boardInformationId);
		return boardInfo ? boardInfo.name : 'Unknown';
	};



	return (
		<Box sx={{p:3}}>
			<Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
				<Typography variant="h4">
					Board Hardware
				</Typography>
				<Button
					variant="contained"
					startIcon={<AddIcon />}
					onClick={() => handleOpen()}
					disabled={boardInfos.length === 0}
				>
					Add Board HW
				</Button>
			</Box>

			{loading && (
				<Box display="flex" justifyContent="center" my={2}>
					<CircularProgress />
				</Box>
			)}

			{boardInfos.length === 0 && (
				<Alert severity="warning" sx={{ mb: 3 }}>
					Please create Board Information first before adding Board Hardware.
				</Alert>
			)}

			<TableContainer component={Paper}>
				<Table>
					<TableHead>
						<TableRow>
							<TableCell>Board HW Name</TableCell>
							<TableCell>Board Information</TableCell>
							<TableCell>Description</TableCell>
							<TableCell align="center">Actions</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{boardHWs.length === 0 ? (
							<TableRow>
								<TableCell colSpan={4} align="center">
									<Box py={4}>
										<MemoryIcon color="disabled" sx={{ fontSize: 48, mb: 2 }} />
										<Typography variant="body1" color="textSecondary">
											No board hardware found. Create your first board hardware.
										</Typography>
									</Box>
								</TableCell>
							</TableRow>
						) : (
							boardHWs.map((boardHW) => (
								<TableRow key={boardHW.id}>
									<TableCell>
										<Chip
											label={boardHW.name}
											color="secondary"
											variant="outlined"
										/>
									</TableCell>
									<TableCell>
										<Chip
											label={getBoardInformationName(boardHW.board_information)}
											color="primary"
											size="small"
										/>
									</TableCell>
									<TableCell>
										<Typography variant="body2" sx={{ maxWidth: 300 }}>
											{typeof boardHW.description === 'object'
												? JSON.stringify(boardHW.description, null, 2)
												: boardHW.description || 'No description'}
										</Typography>
									</TableCell>
									<TableCell align="center">
										<IconButton
											size="small"
											onClick={() => handleOpen(boardHW)}
											color="primary"
										>
											<EditIcon />
										</IconButton>
										<IconButton
											size="small"
											onClick={() => handleDelete(boardHW.id)}
											color="error"
										>
											<DeleteIcon />
										</IconButton>
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</TableContainer>

			<Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
				<DialogTitle>
					{editingBoardHW ? 'Edit Board HW' : 'Add New Board HW'}
				</DialogTitle>
				<DialogContent>
					<TextField
						autoFocus
						margin="dense"
						label="Board HW Name"
						fullWidth
						variant="outlined"
						value={formData.name}
						onChange={(e) => setFormData({ ...formData, name: e.target.value })}
						sx={{ mb: 2 }}
					/>

					<FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
						<InputLabel>Board Information</InputLabel>
						<Select
							value={formData.board_information}
							onChange={(e) => setFormData({ ...formData, board_information: e.target.value })}
							label="Board Information"
						>
							{boardInfos.map((boardInfo) => (
								<MenuItem key={boardInfo.id} value={boardInfo.id}>
									{boardInfo.name}
								</MenuItem>
							))}
						</Select>
					</FormControl>

					<TextField
						margin="dense"
						label="Description (JSON Format)"
						fullWidth
						multiline
						rows={6}
						variant="outlined"
						value={typeof formData.description === 'object'
							? JSON.stringify(formData.description, null, 2)
							: formData.description}
						onChange={(e) => {
							try {
								const parsed = JSON.parse(e.target.value);
								setFormData({ ...formData, description: parsed });
							} catch {
								setFormData({ ...formData, description: e.target.value });
							}
						}}
						placeholder='{"cpu": "ARM Cortex-A72", "memory": "4GB", "storage": "32GB eMMC"}'
						sx={{ mb: 2 }}
					/>
				</DialogContent>
				<DialogActions>
					<Button onClick={handleClose}>Cancel</Button>
					<Button
						onClick={handleSubmit}
						variant="contained"
						disabled={!formData.name.trim() || !formData.board_information || !formData.description}
					>
						{editingBoardHW ? 'Update' : 'Create'}
					</Button>
				</DialogActions>
			</Dialog>

			<Snackbar
				open={snackbar.open}
				autoHideDuration={6000}
				onClose={() => setSnackbar({ ...snackbar, open: false })}
			>
				<Alert
					onClose={() => setSnackbar({ ...snackbar, open: false })}
					severity={snackbar.severity}
					sx={{ width: '100%' }}
				>
					{snackbar.message}
				</Alert>
			</Snackbar>
		</Box>
	);
};

export default BoardHW;
