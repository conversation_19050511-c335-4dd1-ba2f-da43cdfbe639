import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typo<PERSON>,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  IconButton,
  Paper,
  Card,
  CardContent,
  CardActions,
  Chip,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Snackbar,
  Grid,
  Pagination,
  LinearProgress,
  Divider,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { testingPlanTotalService } from '../services';

const TestingPlanDashboard = () => {
  const navigate = useNavigate();
  const [testingPlanTotals, setTestingPlanTotals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [choices, setChoices] = useState({});
  const [pagination, setPagination] = useState({
    count: 0,
    page: 1,
    page_size: 10
  });

  // Dialog states
  const [open, setOpen] = useState(false);
  const [editingTotal, setEditingTotal] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    type: 'Release',
    description: '{}',
    common: '{}',
    information: '',
    date_from: '',
    date_to: ''
  });

  // Filters
  const [filters, setFilters] = useState({
    type: '',
    search: ''
  });

  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    loadTestingPlanTotals();
    loadChoices();
  }, [pagination.page, filters]);

  const loadTestingPlanTotals = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        page_size: pagination.page_size,
        ...filters
      };
      const data = await testingPlanTotalService.getTestingPlanTotals(params);
      setTestingPlanTotals(data.results || []);
      setPagination(prev => ({
        ...prev,
        count: Math.ceil((data.count || 0) / pagination.page_size)
      }));
    } catch (error) {
      showSnackbar('Error loading testing plan totals: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadChoices = async () => {
    try {
      const data = await testingPlanTotalService.getChoices();
      setChoices(data || {});
    } catch (error) {
      console.error('Error loading choices:', error);
    }
  };

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleOpen = (total = null) => {
    if (total) {
      setEditingTotal(total);
      setFormData({
        name: total.name,
        type: total.type,
        description: typeof total.description === 'object' ? JSON.stringify(total.description, null, 2) : total.description,
        common: typeof total.common === 'object' ? JSON.stringify(total.common, null, 2) : total.common,
        information: total.information,
        date_from: total.date_from,
        date_to: total.date_to
      });
    } else {
      setEditingTotal(null);
      setFormData({
        name: '',
        type: 'Release',
        description: '{}',
        common: '{}',
        information: '',
        date_from: '',
        date_to: ''
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingTotal(null);
  };

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.date_from || !formData.date_to) {
      showSnackbar('Name, date from, and date to are required', 'error');
      return;
    }

    // Validate JSON fields
    try {
      if (formData.description.trim()) {
        JSON.parse(formData.description);
      }
      if (formData.common.trim()) {
        JSON.parse(formData.common);
      }
    } catch (error) {
      showSnackbar('Description and Common must be valid JSON format', 'error');
      return;
    }

    try {
      const submitData = {
        ...formData,
        description: formData.description.trim() ? JSON.parse(formData.description) : {},
        common: formData.common.trim() ? JSON.parse(formData.common) : {}
      };

      if (editingTotal) {
        await testingPlanTotalService.updateTestingPlanTotal(editingTotal.id, submitData);
        showSnackbar('Testing plan total updated successfully', 'success');
      } else {
        await testingPlanTotalService.createTestingPlanTotal(submitData);
        showSnackbar('Testing plan total created successfully', 'success');
      }

      handleClose();
      loadTestingPlanTotals();
    } catch (error) {
      showSnackbar('Error saving testing plan total: ' + error.message, 'error');
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this testing plan total?')) {
      try {
        await testingPlanTotalService.deleteTestingPlanTotal(id);
        showSnackbar('Testing plan total deleted successfully', 'success');
        loadTestingPlanTotals();
      } catch (error) {
        showSnackbar('Error deleting testing plan total: ' + error.message, 'error');
      }
    }
  };

  const handleTriggerJenkins = async (id, name) => {
    if (window.confirm(`Are you sure you want to trigger Jenkins build for "${name}"?`)) {
      try {
        console.log("Trigger build id", id);
        const result = await testingPlanTotalService.triggerJenkinsBuildTotal(id);
        showSnackbar(`Jenkins build triggered successfully. ${result.triggered_plans} plans started.`, 'success');
        loadTestingPlanTotals();
      } catch (error) {
        showSnackbar('Error triggering Jenkins build: ' + error.message, 'error');
      }
    }
  };

  const getTypeColor = (type) => {
    const colors = {
      'Release': 'success',
      'PR': 'warning',
      'Nightly': 'info',
      'Weekly': 'secondary'
    };
    return colors[type] || 'default';
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 80) return 'success';
    if (percentage >= 50) return 'warning';
    return 'error';
  };

  return (
    <Box sx={{p: 3}}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Testing Plan Dashboard</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Create Testing Plan Total
        </Button>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, md: 4 }}>
            <FormControl fullWidth size="small">
              <InputLabel>Filter by Type</InputLabel>
              <Select
                value={filters.type}
                onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                label="Filter by Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {choices.testing_plan_total_type_choices?.map(([value, label]) => (
                  <MenuItem key={value} value={value}>{label}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              size="small"
              label="Search by name"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => setFilters({ type: '', search: '' })}
            >
              Clear
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Testing Plan Totals Grid */}
      <Grid container spacing={3}>
        {testingPlanTotals.map((total) => (
          <Grid size={{ xs: 12, md: 6, lg: 4 }} key={total.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
                    {total.name}
                  </Typography>
                  <Chip
                    label={total.type}
                    color={getTypeColor(total.type)}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {total.information || 'No information provided'}
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Period: {total.date_from} to {total.date_to}
                  </Typography>
                </Box>

                {/* Progress Summary */}
                {total.progress_summary && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      Progress: {total.progress_summary.completed}/{total.progress_summary.total} plans
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={(total.progress_summary.completed / total.progress_summary.total) * 100}
                      color={getProgressColor((total.progress_summary.completed / total.progress_summary.total) * 100)}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Box display="flex" justifyContent="space-between" mt={1}>
                      <Typography variant="caption">
                        Running: {total.progress_summary.running}
                      </Typography>
                      <Typography variant="caption">
                        Failed: {total.progress_summary.failed}
                      </Typography>
                    </Box>
                  </Box>
                )}

                <Typography variant="caption" color="text.secondary">
                  Created: {new Date(total.created_at).toLocaleDateString()}
                </Typography>
              </CardContent>

              <CardActions>
                <Button
                  size="small"
                  startIcon={<PlayIcon />}
                  onClick={() => handleTriggerJenkins(total.id, total.name)}
                  color="success"
                >
                  Trigger Build
                </Button>
                <Button
                  size="small"
                  startIcon={<SettingsIcon />}
                  onClick={() => navigate(`/testing-plan-detail/${total.id}`)}
                >
                  Manage
                </Button>
                <Box sx={{ ml: 'auto' }}>
                  <IconButton
                    size="small"
                    onClick={() => handleOpen(total)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDelete(total.id)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Pagination */}
      {pagination.count > 1 && (
        <Box display="flex" justifyContent="center" mt={4}>
          <Pagination
            count={pagination.count}
            page={pagination.page}
            onChange={(event, value) => setPagination({ ...pagination, page: value })}
            color="primary"
            shape="rounded"
          />
        </Box>
      )}

      {/* Create/Edit Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingTotal ? 'Edit Testing Plan Total' : 'Create New Testing Plan Total'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
            required
          />

          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Type</InputLabel>
            <Select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              label="Type"
            >
              {choices.testing_plan_total_type_choices?.map(([value, label]) => (
                <MenuItem key={value} value={value}>{label}</MenuItem>
              ))}
            </Select>
          </FormControl>

          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={6}>
              <TextField
                label="Date From"
                type="date"
                fullWidth
                variant="outlined"
                value={formData.date_from}
                onChange={(e) => setFormData({ ...formData, date_from: e.target.value })}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Date To"
                type="date"
                fullWidth
                variant="outlined"
                value={formData.date_to}
                onChange={(e) => setFormData({ ...formData, date_to: e.target.value })}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
          </Grid>

          <TextField
            margin="dense"
            label="Information"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            value={formData.information}
            onChange={(e) => setFormData({ ...formData, information: e.target.value })}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label="Description (JSON format)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            sx={{ mb: 2 }}
            helperText="Enter valid JSON format"
          />

          <TextField
            margin="dense"
            label="Common (JSON format)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={formData.common}
            onChange={(e) => setFormData({ ...formData, common: e.target.value })}
            helperText="Enter valid JSON format"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name.trim() || !formData.date_from || !formData.date_to}
          >
            {editingTotal ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TestingPlanDashboard;
