import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Button,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Api as ApiIcon,
  Assignment as TestCaseIcon,
  Memory as BoardIcon,
  Settings as ConfigIcon,
  PlayArrow as PlanIcon,
} from '@mui/icons-material';
import TestCaseTable from '../components/TestCaseTable';
import { 
  testcaseService, 
  boardService, 
  configService, 
  testingPlanService 
} from '../services';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`api-demo-tabpanel-${index}`}
    aria-labelledby={`api-demo-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const ApiDemo = () => {
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const testApiConnection = async () => {
    try {
      // Test each service
      const testCaseChoices = await testcaseService.getChoices();
      const boardChoices = await boardService.getChoices();
      const configChoices = await configService.getChoices();
      const planChoices = await testingPlanService.getChoices();

      showSnackbar('All API connections successful!', 'success');
      console.log('API Test Results:', {
        testCaseChoices,
        boardChoices,
        configChoices,
        planChoices
      });
    } catch (error) {
      showSnackbar('API connection failed: ' + error.message, 'error');
      console.error('API Test Error:', error);
    }
  };

  const testDataFetch = async () => {
    try {
      const [testCases, boardInfo, buildConfigs, testingPlans] = await Promise.all([
        testcaseService.getTestCases(),
        boardService.getBoardInformation(),
        configService.getBuildConfigurations(),
        testingPlanService.getTestingPlans()
      ]);

      showSnackbar('Data fetch successful!', 'success');
      console.log('Data Fetch Results:', {
        testCases: testCases.results || testCases,
        boardInfo: boardInfo.results || boardInfo,
        buildConfigs: buildConfigs.results || buildConfigs,
        testingPlans: testingPlans.results || testingPlans
      });
    } catch (error) {
      showSnackbar('Data fetch failed: ' + error.message, 'error');
      console.error('Data Fetch Error:', error);
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          <ApiIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          API Demo & Testing
        </Typography>
        <Box>
          <Button
            variant="outlined"
            onClick={testApiConnection}
            sx={{ mr: 1 }}
          >
            Test API Connection
          </Button>
          <Button
            variant="contained"
            onClick={testDataFetch}
          >
            Test Data Fetch
          </Button>
        </Box>
      </Box>

      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<TestCaseIcon />}
            label="Test Cases"
            iconPosition="start"
          />
          <Tab
            icon={<BoardIcon />}
            label="Board Management"
            iconPosition="start"
          />
          <Tab
            icon={<ConfigIcon />}
            label="Configurations"
            iconPosition="start"
          />
          <Tab
            icon={<PlanIcon />}
            label="Testing Plans"
            iconPosition="start"
          />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            Test Case Management API Demo
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            This demo shows the integration with Django TestCase API endpoints.
            You can create, read, update, and delete test cases.
          </Typography>
          <TestCaseTable />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Board Management API Demo
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            This demo shows the integration with Django Board API endpoints.
            Includes Board Information, Board HW, and Board Topology management.
          </Typography>
          <Alert severity="info">
            Board Management API integration is ready. 
            You can implement similar components like TestCaseTable for Board Information, Board HW, and Board Topology.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Configuration Management API Demo
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            This demo shows the integration with Django Configuration API endpoints.
            Includes Build Configuration and Board Configuration management.
          </Typography>
          <Alert severity="info">
            Configuration API integration is ready.
            You can implement components for Build Configuration and Board Configuration management.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Testing Plan API Demo
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            This demo shows the integration with Django Testing Plan API endpoints.
            Includes Testing Plan and Testing Plan Batch management, plus Jenkins integration.
          </Typography>
          <Alert severity="info">
            Testing Plan API integration is ready.
            You can implement components for Testing Plan management and Jenkins build triggering.
          </Alert>
        </TabPanel>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ApiDemo;
