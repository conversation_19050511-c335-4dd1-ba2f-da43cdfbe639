import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Snackbar,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { configService, boardService } from '../services';

const BoardConfiguration = () => {
  const [boardConfigs, setBoardConfigs] = useState([]);
  const [boardInfos, setBoardInfos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    priority: 100,
    description: {},
    board_information: '',
    status: 'Active',
  });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    loadBoardConfigs();
    loadBoardInfos();
  }, []);

  const loadBoardConfigs = async () => {
    try {
      setLoading(true);
      const data = await configService.getBoardConfigurations();
      setBoardConfigs(data.results || data);
    } catch (error) {
      showSnackbar('Error loading board configurations: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadBoardInfos = async () => {
    try {
      const data = await boardService.getBoardInformation();
      setBoardInfos(data.results || data);
    } catch (error) {
      console.error('Error loading board information:', error);
    }
  };

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };





  const handleOpen = (config = null) => {
    if (config) {
      setEditingConfig(config);
      setFormData({
        name: config.name,
        priority: config.priority,
        description: config.description || {},
        board_information: config.board_information,
        status: config.status,
      });
    } else {
      setEditingConfig(null);
      setFormData({
        name: '',
        priority: 100,
        description: {},
        board_information: '',
        status: 'Active',
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingConfig(null);
    setFormData({
      name: '',
      priority: 100,
      description: {},
      board_information: '',
      status: 'Active',
    });
  };

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.board_information) {
      showSnackbar('Board Configuration name and Board Information are required', 'error');
      return;
    }

    try {
      setLoading(true);
      const configData = {
        name: formData.name.trim(),
        priority: formData.priority,
        description: formData.description,
        board_information: formData.board_information,
        status: formData.status,
      };

      if (editingConfig) {
        await configService.updateBoardConfiguration(editingConfig.id, configData);
        showSnackbar('Board configuration updated successfully');
      } else {
        await configService.createBoardConfiguration(configData);
        showSnackbar('Board configuration created successfully');
      }

      loadBoardConfigs();
      handleClose();
    } catch (error) {
      showSnackbar('Error saving board configuration: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this board configuration?')) {
      try {
        setLoading(true);
        await configService.deleteBoardConfiguration(id);
        showSnackbar('Board configuration deleted successfully');
        loadBoardConfigs();
      } catch (error) {
        showSnackbar('Error deleting board configuration: ' + error.message, 'error');
      } finally {
        setLoading(false);
      }
    }
  };

  const getBoardInformationName = (boardInformationId) => {
    const board = boardInfos.find(b => b.id === boardInformationId);
    return board ? board.name : 'Unknown Board';
  };

  return (
    <Box sx={{p:3}}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Board Configuration</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add Configuration
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Configuration Name</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Board Information</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Created</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {boardConfigs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Box py={4}>
                    <Typography variant="body1" color="textSecondary">
                      No board configurations found. Create your first configuration.
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              boardConfigs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell>
                    <Chip
                      label={config.name}
                      color="primary"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={config.priority}
                      color="primary"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={config.board_information_name || getBoardInformationName(config.board_information)}
                      color="secondary"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ maxWidth: 250, overflow: 'hidden' }}>
                      <Typography variant="body2" noWrap>
                        {typeof config.description === 'object'
                          ? JSON.stringify(config.description)
                          : config.description || 'No description'}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {new Date(config.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleOpen(config)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(config.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingConfig ? 'Edit Board Configuration' : 'Add New Board Configuration'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Board Configuration Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
            required
          />

          {/* <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Priority</InputLabel>
            <Select
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
              label="Priority"
            >
              {priorities.map((priority) => (
                <MenuItem key={priority} value={priority}>
                  {priority}
                </MenuItem>
              ))}
            </Select>
          </FormControl> */}
          <TextField
            fullWidth
            type="number"
            sx={{ mb: 2 }}
            label="Priority"
            value={formData.priority}
            onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
          />

          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Board Information</InputLabel>
            <Select
              value={formData.board_information}
              onChange={(e) => setFormData({ ...formData, board_information: e.target.value })}
              label="Board Information"
            >
              {boardInfos.map((board) => (
                <MenuItem key={board.id} value={board.id}>
                  {board.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value })}
              label="Status"
            >
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
              <MenuItem value="Draft">Draft</MenuItem>
            </Select>
          </FormControl>

          <TextField
            margin="dense"
            label="Description (JSON format)"
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={typeof formData.description === 'object'
              ? JSON.stringify(formData.description, null, 2)
              : formData.description}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                setFormData({ ...formData, description: parsed });
              } catch {
                setFormData({ ...formData, description: e.target.value });
              }
            }}
            placeholder='{"key": "value"}'
            helperText="Enter JSON format description"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name.trim() || !formData.board_information}
          >
            {editingConfig ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BoardConfiguration;
