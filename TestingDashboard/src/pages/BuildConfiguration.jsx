import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Snackbar,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { configService, boardService } from '../services';

const BuildConfiguration = () => {
  const [buildConfigs, setBuildConfigs] = useState([]);
  const [boardInfos, setBoardInfos] = useState([]);
  const [open, setOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    board_information: '',
    // build_type: 'Release',
    // target_platform: '',
    // compiler_flags: '',
    // environment_variables: '',
    // dependencies: '',
    priority: 100,
  });
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    loadBuildConfigs();
    loadBoardInfos();
  }, []);

  const loadBuildConfigs = async () => {
    try {
      setLoading(true);
      const data = await configService.getBuildConfigurations();
      setBuildConfigs(data.results || data);
    } catch (error) {
      showSnackbar('Error loading build configurations: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadBoardInfos = async () => {
    try {
      const data = await boardService.getBoardInformation();
      setBoardInfos(data.results || data);
    } catch (error) {
      console.error('Error loading board information:', error);
    }
  };

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  // const buildTypes = ['Debug', 'Release', 'RelWithDebInfo', 'MinSizeRel'];
  // const [priorities, setPriorities] = useState(["Low", "Medium", "High", "Critical"]);

  const handleOpen = (config = null) => {
    if (config) {
      setEditingConfig(config);
      setFormData({
        name: config.name,
        description: config.description,
        board_information: config.board_information,
        // target_platform: config.target_platform || '',
        priority: config.priority || 100,
      });
    } else {
      setEditingConfig(null);
      setFormData({
        name: '',
        description: '',
        board_information: '',
        // target_platform: '',
        priority: 100,
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingConfig(null);
    setFormData({
      name: '',
      description: '',
      board_information: '',
      // target_platform: '',
      priority: 100,
    });
  };

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.boardInformationId) {
      showSnackbar('Configuration name and Board Information are required', 'error');
      return;
    }

    try {
      setLoading(true);
      const configData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        board_information: formData.boardInformationId,
        // target_platform: formData.target_platform.trim(),
        priority: formData.priority,
      };

      if (editingConfig) {
        await configService.updateBuildConfiguration(editingConfig.id, configData);
        showSnackbar('Build configuration updated successfully');
      } else {
        await configService.createBuildConfiguration(configData);
        showSnackbar('Build configuration created successfully');
      }

      loadBuildConfigs();
      handleClose();
    } catch (error) {
      showSnackbar('Error saving build configuration: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this build configuration?')) {
      try {
        setLoading(true);
        await configService.deleteBuildConfiguration(id);
        showSnackbar('Build configuration deleted successfully');
        loadBuildConfigs();
      } catch (error) {
        showSnackbar('Error deleting build configuration: ' + error.message, 'error');
      } finally {
        setLoading(false);
      }
    }
  };

  // const handleDelete = (id) => {
  //   if (window.confirm('Are you sure you want to delete this build configuration?')) {
  //     setBuildConfigs(buildConfigs.filter(config => config.id !== id));
  //     setSnackbar({
  //       open: true,
  //       message: 'Build configuration deleted successfully',
  //       severity: 'success',
  //     });
  //   }
  // };

  const getBoardInformationName = (boardInformationId) => {
    const board = boardInfos.find(b => b.id === boardInformationId);
    return board ? board.name : 'Unknown Board';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'Low': 'default',
      'Medium': 'primary',
      'High': 'warning',
      'Critical': 'error',
    };
    return colors[priority] || 'default';
  };

  const formatDescription = (description) => {
    if (typeof (description) === 'object') {
      return JSON.stringify(description, null, 2);
    }
    if (!description) return 'No description';
    try {
      const parsed = JSON.parse(description);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return description;
    }
  };


  return (
    <Box sx={{p: 3}}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Build Configuration</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add Configuration
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Configuration Name</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Board Information</TableCell>
              <TableCell>Description</TableCell>
              {/* <TableCell>Created</TableCell> */}
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {buildConfigs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Box py={4}>
                    <Typography variant="body1" color="textSecondary">
                      No build configurations found. Create your first configuration.
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              buildConfigs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell>
                    <Chip
                      label={config.name}
                      color="primary"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={config.priority}
                      color={getPriorityColor(config.priority)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getBoardInformationName(config.board_information)}
                      color="secondary"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ maxWidth: 250, overflow: 'hidden' }}>
                      <pre style={{
                        fontSize: '12px',
                        margin: 0,
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word'
                      }}>
                        {formatDescription(config.description).substring(0, 80)}
                        {formatDescription(config.description).length > 80 && '...'}
                      </pre>
                    </Box>
                  </TableCell>
                  {/* <TableCell>
                    {new Date(config.createdAt).toLocaleDateString()}
                  </TableCell> */}
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleOpen(config)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(config.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingConfig ? 'Edit Build Configuration' : 'Add New Build Configuration'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Configuration Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
            required
          />

          {/* <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Priority</InputLabel>
            <Select
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
              label="Priority"
            >
              {priorities.map((priority) => (
                <MenuItem key={priority} value={priority}>
                  {priority}
                </MenuItem>
              ))}
            </Select>
          </FormControl> */}
          <TextField
            fullWidth
            sx={{ mb: 2 }}
            label="Priority"
            value={formData.priority}
            onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
          />

          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Board Information</InputLabel>
            <Select
              value={formData.boardInformationId}
              onChange={(e) => setFormData({ ...formData, boardInformationId: e.target.value })}
              label="Board Information"
            >
              {boardInfos.map((board) => (
                <MenuItem key={board.id} value={board.id}>
                  {board.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            margin="dense"
            label="Description (JSON format)"
            fullWidth
            multiline
            rows={6}
            variant="outlined"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder='{"build_type": "release", "optimization": "O2", "features": ["networking", "graphics"]}'
            helperText="Enter build configuration in JSON format"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name.trim() || !formData.boardInformationId}
          >
            {editingConfig ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BuildConfiguration;
