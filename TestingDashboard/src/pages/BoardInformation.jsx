import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Alert,
  Snackbar,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { boardService } from '../services';

const BoardInformation = () => {
  const [boardInfos, setBoardInfos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editingBoard, setEditingBoard] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: {},
    status: 'Active',
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });
  const [choices, setChoices] = useState({});

  // Load data on component mount
  useEffect(() => {
    loadBoardInformations();
    loadChoices();
  }, []);

  const loadBoardInformations = async () => {
    try {
      setLoading(true);
      const data = await boardService.getBoardInformation();
      setBoardInfos(data.results || data);
    } catch (error) {
      showSnackbar('Error loading board information: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadChoices = async () => {
    try {
      const data = await boardService.getChoices();
      setChoices(data);
    } catch (error) {
      console.error('Error loading choices:', error);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  };

  const handleOpen = (board = null) => {
    if (board) {
      setEditingBoard(board);
      setFormData({
        name: board.name,
        description: typeof board.description === 'object' ? JSON.stringify(board.description, null, 2) : board.description || '',
        status: board.status || 'Active',
      });
    } else {
      setEditingBoard(null);
      setFormData({
        name: '',
        description: '',
        status: 'Active',
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingBoard(null);
    setFormData({
      name: '',
      description: '',
      status: 'Active',
    });
  };

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      showSnackbar('Board name is required', 'error');
      return;
    }

    // Validate JSON description
    let descriptionObj = {};
    try {
      if (formData.description.trim()) {
        descriptionObj = JSON.parse(formData.description);
      }
    } catch (error) {
      showSnackbar('Description must be valid JSON format', 'error');
      return;
    }

    const boardData = {
      name: formData.name.trim(),
      description: descriptionObj,
      status: formData.status,
    };

    try {
      setLoading(true);
      if (editingBoard) {
        await boardService.updateBoardInformation(editingBoard.id, boardData);
        showSnackbar('Board information updated successfully');
      } else {
        await boardService.createBoardInformation(boardData);
        showSnackbar('Board information created successfully');
      }

      handleClose();
      loadBoardInformations(); // Reload data
    } catch (error) {
      showSnackbar('Error saving board information: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this board information?')) {
      try {
        setLoading(true);
        await boardService.deleteBoardInformation(id);
        showSnackbar('Board information deleted successfully');
        loadBoardInformations(); // Reload data
      } catch (error) {
        showSnackbar('Error deleting board information: ' + error.message, 'error');
      } finally {
        setLoading(false);
      }
    }
  };

  const formatDescription = (description) => {
    if (!description) return 'No description';
    if (typeof description === 'object') {
      return JSON.stringify(description, null, 2);
    }
    try {
      const parsed = JSON.parse(description);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return description;
    }
  };

  return (
    <Box sx={{p:3}}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Board Information
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add Board Information
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Board Name</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Created</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {boardInfos.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Box py={4}>
                    <InfoIcon color="disabled" sx={{ fontSize: 48, mb: 2 }} />
                    <Typography variant="body1" color="textSecondary">
                      No board information found. Create your first board information.
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              boardInfos.map((board) => (
                <TableRow key={board.id}>
                  <TableCell>
                    <Chip
                      label={board.name}
                      color="primary"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={board.status || 'Active'}
                      color={board.status === 'Active' ? 'success' : board.status === 'Inactive' ? 'error' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ maxWidth: 300, overflow: 'hidden' }}>
                      <pre style={{
                        fontSize: '12px',
                        margin: 0,
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word'
                      }}>
                        {formatDescription(board.description).substring(0, 100)}
                        {formatDescription(board.description).length > 100 && '...'}
                      </pre>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {board.created_at ? new Date(board.created_at).toLocaleDateString() : 'N/A'}
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleOpen(board)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(board.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingBoard ? 'Edit Board Information' : 'Add New Board Information'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Board Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
          />

          <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={formData.status}
              label="Status"
              onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            >
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
              <MenuItem value="Maintenance">Maintenance</MenuItem>
            </Select>
          </FormControl>

          <TextField
            margin="dense"
            label="Description (JSON format)"
            fullWidth
            multiline
            rows={8}
            variant="outlined"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder='{"cpu": "ARM Cortex-A72", "memory": "4GB", "storage": "32GB eMMC"}'
            helperText="Enter board specifications in JSON format"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name.trim()}
          >
            {editingBoard ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BoardInformation;
