import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  Avatar,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  Engineering as EngineerIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { useLocalStorage } from '../hooks/useLocalStorage';

const USER_ROLES = [
  { value: 'admin', label: 'Administrator', icon: <AdminIcon /> },
  { value: 'engineer', label: 'Engineer', icon: <EngineerIcon /> },
  { value: 'viewer', label: 'Viewer', icon: <ViewIcon /> },
];

const UserManagement = () => {
  const [users, setUsers] = useLocalStorage('users', [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      fullName: 'System Administrator',
      role: 'admin',
      status: 'active',
      createdDate: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-15T10:30:00.000Z',
    },
    {
      id: 2,
      username: 'engineer1',
      email: '<EMAIL>',
      fullName: 'John Engineer',
      role: 'engineer',
      status: 'active',
      createdDate: '2024-01-05T00:00:00.000Z',
      lastLogin: '2024-01-14T15:45:00.000Z',
    },
    {
      id: 3,
      username: 'viewer1',
      email: '<EMAIL>',
      fullName: 'Jane Viewer',
      role: 'viewer',
      status: 'inactive',
      createdDate: '2024-01-10T00:00:00.000Z',
      lastLogin: null,
    },
  ]);

  const [open, setOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    fullName: '',
    role: 'viewer',
    status: 'active',
    password: '',
  });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  const handleOpen = (user = null) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        status: user.status,
        password: '',
      });
    } else {
      setEditingUser(null);
      setFormData({
        username: '',
        email: '',
        fullName: '',
        role: 'viewer',
        status: 'active',
        password: '',
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingUser(null);
    setFormData({
      username: '',
      email: '',
      fullName: '',
      role: 'viewer',
      status: 'active',
      password: '',
    });
  };

  const handleSubmit = () => {
    if (editingUser) {
      // Update existing user
      setUsers(users.map(user =>
        user.id === editingUser.id
          ? {
            ...user,
            ...formData,
            // Don't update password if it's empty
            ...(formData.password ? {} : { password: undefined })
          }
          : user
      ));
      setSnackbar({
        open: true,
        message: 'User updated successfully',
        severity: 'success',
      });
    } else {
      // Create new user
      const newUser = {
        id: Date.now(),
        ...formData,
        createdDate: new Date().toISOString(),
        lastLogin: null,
      };
      setUsers([...users, newUser]);
      setSnackbar({
        open: true,
        message: 'User created successfully',
        severity: 'success',
      });
    }
    handleClose();
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      setUsers(users.filter(user => user.id !== id));
      setSnackbar({
        open: true,
        message: 'User deleted successfully',
        severity: 'success',
      });
    }
  };

  const handleStatusToggle = (user) => {
    const newStatus = user.status === 'active' ? 'inactive' : 'active';
    setUsers(users.map(u =>
      u.id === user.id
        ? { ...u, status: newStatus }
        : u
    ));
    setSnackbar({
      open: true,
      message: `User ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`,
      severity: 'info',
    });
  };

  const getRoleIcon = (role) => {
    const roleConfig = USER_ROLES.find(r => r.value === role);
    return roleConfig ? roleConfig.icon : <PersonIcon />;
  };

  const getRoleLabel = (role) => {
    const roleConfig = USER_ROLES.find(r => r.value === role);
    return roleConfig ? roleConfig.label : role;
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'success' : 'default';
  };

  const getAvatarColor = (role) => {
    const colors = {
      'admin': '#f44336',
      'engineer': '#2196f3',
      'viewer': '#4caf50',
    };
    return colors[role] || '#9e9e9e';
  };

  return (
    <Box sx={{p: 3}}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">User Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add User
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created Date</TableCell>
              <TableCell>Last Login</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar
                      sx={{
                        bgcolor: getAvatarColor(user.role),
                        width: 40,
                        height: 40,
                      }}
                    >
                      {getRoleIcon(user.role)}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {user.fullName}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        @{user.username}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {user.email}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getRoleIcon(user.role)}
                    label={getRoleLabel(user.role)}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Chip
                      label={user.status}
                      color={getStatusColor(user.status)}
                      size="small"
                    />
                    <Switch
                      checked={user.status === 'active'}
                      onChange={() => handleStatusToggle(user)}
                      size="small"
                    />
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {new Date(user.createdDate).toLocaleDateString()}
                  </Typography>
                </TableCell>
                <TableCell>
                  {user.lastLogin ? (
                    <Typography variant="body2">
                      {new Date(user.lastLogin).toLocaleString()}
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      Never
                    </Typography>
                  )}
                </TableCell>
                <TableCell align="right">
                  <Tooltip title="Edit User">
                    <IconButton
                      size="small"
                      onClick={() => handleOpen(user)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete User">
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(user.id)}
                      color="error"
                      disabled={user.username === 'admin'}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Username"
            fullWidth
            variant="outlined"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            sx={{ mb: 2 }}
            disabled={editingUser?.username === 'admin'}
          />

          <TextField
            margin="dense"
            label="Full Name"
            fullWidth
            variant="outlined"
            value={formData.fullName}
            onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label="Email"
            type="email"
            fullWidth
            variant="outlined"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label={editingUser ? "New Password (leave blank to keep current)" : "Password"}
            type="password"
            fullWidth
            variant="outlined"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            sx={{ mb: 2 }}
            required={!editingUser}
          />

          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Role</InputLabel>
            <Select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              label="Role"
              disabled={editingUser?.username === 'admin'}
            >
              {USER_ROLES.map((role) => (
                <MenuItem key={role.value} value={role.value}>
                  <Box display="flex" alignItems="center" gap={1}>
                    {role.icon}
                    {role.label}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth variant="outlined">
            <InputLabel>Status</InputLabel>
            <Select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value })}
              label="Status"
              disabled={editingUser?.username === 'admin'}
            >
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
            </Select>
          </FormControl>

          {editingUser?.username === 'admin' && (
            <Alert severity="info" sx={{ mt: 2 }}>
              Admin user cannot be modified or deleted for security reasons.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={
              !formData.username ||
              !formData.email ||
              !formData.fullName ||
              (!editingUser && !formData.password)
            }
          >
            {editingUser ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserManagement;
