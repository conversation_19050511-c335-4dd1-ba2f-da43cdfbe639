import { useState, useEffect } from 'react';
import {
	<PERSON><PERSON><PERSON>,
	<PERSON>ton,
	<PERSON>,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	MenuItem,
	IconButton,
	Paper,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Chip,
	FormControl,
	InputLabel,
	Select,
	Alert,
	Snackbar,
	Grid,
	Card,
	CardContent,
	CardActions,
	Tabs,
	Tab,
	OutlinedInput,
	Checkbox,
	ListItemText,
	ButtonGroup,
	ToggleButtonGroup,
	ToggleButton,
	Stack,
	Pagination,
	Accordion,
	AccordionSummary,
	AccordionDetails,
	InputAdornment,
} from '@mui/material';
import {
	Add as AddIcon,
	Edit as EditIcon,
	Delete as DeleteIcon,
	PlayArrow as PlayIcon,
	Visibility as ViewIcon,
	Settings as SettingsIcon,
	ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { testingPlanService, testingPlanTotalService, configService, testcaseService, boardService } from '../services';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
	PaperProps: {
		style: {
			maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
			width: 250,
		},
	},
};

const TestingPlan = () => {
	const [testingPlans, setTestingPlans] = useState([]);
	const [buildConfigs, setBuildConfigs] = useState([]);
	const [testCases, setTestCases] = useState([]);
	const [boardInfos, setBoardInfos] = useState([]);
	const [loading, setLoading] = useState(false);

	const [open, setOpen] = useState(false);
	const [bconfigOpen, setBconfigOpen] = useState(false);
	const [batchOpen, setBatchOpen] = useState(false);
	const [editingPlan, setEditingPlan] = useState(null);
	const [editingBconfig, setEditingBconfig] = useState(null);
	const [currentBconfig, setCurrentBconfig] = useState(null);
	const [tabValue, setTabValue] = useState(0);
	const [formData, setFormData] = useState({
		name: '',
		description: '',
		common: '',
		information: '',
		dateFrom: '',
		dateTo: '',
		tag: 'release',
		buildConfigurations: [],
	});
	const [bconfigFormData, setBconfigFormData] = useState({
		name: '',
		status: 'Active',
		description: '',
		testcases: [],
		tag: 'release',
		boardInformationId: '',
	});
	const [batchFormData, setBatchFormData] = useState({
		name: '',
		// description: '',
		testcases: [],
		// priority: 'Medium',
	});
	const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

	const tags = ['release', 'pr', 'nightly', 'weekly'];
	const [tagSelected, setTagSelected] = useState(tags[0]);

	useEffect(() => {
		loadTestingPlans();
		loadBuildConfigs();
		loadTestCases();
		loadBoardInfos();
	}, []);

	const loadTestingPlans = async () => {
		try {
			setLoading(true);
			const data = await testingPlanTotalService.getTestingPlans();
			setTestingPlans(data.results || []);
			console.log('Testing Plans:', data.results || []);
		} catch (error) {
			showSnackbar('Error loading testing plans: ' + error.message, 'error');
		} finally {
			setLoading(false);
		}
	};

	const loadBuildConfigs = async () => {
		try {
			const data = await configService.getBuildConfigurations();
			setBuildConfigs(data.results || data);
			console.log('Build Configs:', data.results || []);
		} catch (error) {
			console.error('Error loading build configs:', error);
		}
	};

	const loadTestCases = async () => {
		try {
			const data = await testcaseService.getTestCases();
			setTestCases(data.results || data);
		} catch (error) {
			console.error('Error loading test cases:', error);
		}
	};

	const loadBoardInfos = async () => {
		try {
			const data = await boardService.getBoardInformation();
			setBoardInfos(data.results || data);
		} catch (error) {
			console.error('Error loading board information:', error);
		}
	};

	const showSnackbar = (message, severity = 'info') => {
		setSnackbar({ open: true, message, severity });
	};

	const handleTagChange = (event, newTag) => {
		setTagSelected(newTag);
	};

	const handleOpen = (plan = null) => {
		if (plan) {
			setEditingPlan(plan);
			setFormData({
				name: plan.name,
				description: plan.description,
				common: plan.common,
				information: plan.information,
				dateFrom: plan.dateFrom,
				dateTo: plan.dateTo,
				tag: plan.tag,
				buildConfigurations: plan.buildConfigurations || [],
			});
		} else {
			setEditingPlan(null);
			setFormData({
				name: '',
				description: '',
				common: '',
				information: '',
				dateFrom: '',
				dateTo: '',
				tag: 'release',
				buildConfigurations: [],
			});
		}
		setOpen(true);
	};

	const handleClose = () => {
		setOpen(false);
		setEditingPlan(null);
		setTabValue(0);
		setFormData({
			name: '',
			description: '',
			common: '',
			information: '',
			dateFrom: '',
			dateTo: '',
			tag: 'release',
			buildConfigurations: [],
		});
	};

	const handleSubmit = async () => {
		if (!formData.name.trim() || !formData.dateFrom || !formData.dateTo) {
			setSnackbar({
				open: true,
				message: 'Testing plan name, date from, and date to are required',
				severity: 'error',
			});
			return;
		}

		// Validate JSON fields
		try {
			if (formData.description.trim()) {
				JSON.parse(formData.description);
			}
			if (formData.common.trim()) {
				JSON.parse(formData.common);
			}
		} catch (error) {
			setSnackbar({
				open: true,
				message: 'Description and Common must be valid JSON format',
				severity: 'error',
			});
			return;
		}

		try {
			const planData = {
				name: formData.name.trim(),
				description: formData.description.trim() ? JSON.parse(formData.description) : {},
				common: formData.common.trim() ? JSON.parse(formData.common) : {},
				information: formData.information.trim(),
				date_from: formData.dateFrom,
				date_to: formData.dateTo,
				status: 'Draft',
				priority: 'Medium',
				board_configurations: formData.buildConfigurations,
				test_cases: []
			};

			if (editingPlan) {
				await testingPlanTotalService.updateTestingPlan(editingPlan.id, planData);
				setSnackbar({
					open: true,
					message: 'Testing plan updated successfully',
					severity: 'success',
				});
			} else {
				await testingPlanTotalService.createTestingPlan(planData);
				setSnackbar({
					open: true,
					message: 'Testing plan created successfully',
					severity: 'success',
				});
			}

			handleClose();
			loadTestingPlans();
		} catch (error) {
			setSnackbar({
				open: true,
				message: 'Error saving testing plan: ' + error.message,
				severity: 'error',
			});
		}
	};

	const handleDelete = async (id) => {
		if (window.confirm('Are you sure you want to delete this testing plan?')) {
			try {
				await testingPlanTotalService.deleteTestingPlan(id);
				setSnackbar({
					open: true,
					message: 'Testing plan deleted successfully',
					severity: 'success',
				});
				loadTestingPlans();
			} catch (error) {
				setSnackbar({
					open: true,
					message: 'Error deleting testing plan: ' + error.message,
					severity: 'error',
				});
			}
		}
	};

	// Build Configuration functions
	const handleBconfigOpen = (bconfig = null) => {
		if (bconfig) {
			setEditingBconfig(bconfig);
			setBconfigFormData({
				name: bconfig.name,
				status: bconfig.status,
				description: bconfig.description,
				testcases: bconfig.testcases || [],
				tag: bconfig.tag,
				boardInformationId: bconfig.boardInformationId,
			});
		} else {
			setEditingBconfig(null);
			setBconfigFormData({
				name: '',
				status: 'Active',
				description: '',
				testcases: [],
				tag: 'release',
				boardInformationId: '',
			});
		}
		setBconfigOpen(true);
	};

	const handleBconfigClose = () => {
		setBconfigOpen(false);
		setEditingBconfig(null);
		setBconfigFormData({
			name: '',
			status: 'Active',
			description: '',
			testcases: [],
			tag: 'release',
			boardInformationId: '',
		});
	};

	const handleBconfigSubmit = () => {
		if (!bconfigFormData.name.trim()) {
			setSnackbar({
				open: true,
				message: 'Build Configuration name is required',
				severity: 'error',
			});
			return;
		}

		const bconfigData = {
			id: editingBconfig ? editingBconfig.id : Date.now().toString(),
			name: bconfigFormData.name.trim(),
			status: bconfigFormData.status,
			description: bconfigFormData.description.trim(),
			testcases: bconfigFormData.testcases,
			tag: bconfigFormData.tag,
			boardInformationId: bconfigFormData.boardInformationId,
			createdAt: editingBconfig ? editingBconfig.createdAt : new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		};

		if (editingBconfig) {
			setBuildConfigs(buildConfigs.map(config =>
				config.id === editingBconfig.id ? bconfigData : config
			));
			setSnackbar({
				open: true,
				message: 'Build Configuration updated successfully',
				severity: 'success',
			});
		} else {
			setBuildConfigs([...buildConfigs, bconfigData]);
			setSnackbar({
				open: true,
				message: 'Build Configuration created successfully',
				severity: 'success',
			});
		}
		handleBconfigClose();
	};

	const handleBconfigDelete = (id) => {
		if (window.confirm('Are you sure you want to delete this build configuration?')) {
			setBuildConfigs(buildConfigs.filter(config => config.id !== id));
			setSnackbar({
				open: true,
				message: 'Build Configuration deleted successfully',
				severity: 'success',
			});
		}
	};

	// Batch functions
	const handleBatchOpen = (config) => {
		setCurrentBconfig(config);
		setBatchFormData({
			name: '',
			// description: '',
			testcases: [],
			// priority: 'Medium',
		});
		setBatchOpen(true);
	};

	const handleBatchClose = () => {
		setBatchOpen(false);
		setCurrentBconfig(null);
		setBatchFormData({
			name: '',
			// description: '',
			testcases: [],
			// priority: 'Medium',
		});
	};

	const handleBatchSubmit = () => {
		if (!batchFormData.name.trim() || batchFormData.testcases.length === 0) {
			setSnackbar({
				open: true,
				message: 'Batch name and at least one test case are required',
				severity: 'error',
			});
			return;
		}

		const batchData = {
			id: Date.now().toString(),
			name: batchFormData.name.trim(),
			// description: batchFormData.description.trim(),
			testcases: batchFormData.testcases,
			// priority: batchFormData.priority,
			createdAt: new Date().toISOString(),
		};

		// Update the current build config with new batch
		const updatedConfig = {
			...currentBconfig,
			batches: [...(currentBconfig.batches || []), batchData],
			updatedAt: new Date().toISOString(),
		};

		setBuildConfigs(buildConfigs.map(config =>
			config.id === currentBconfig.id ? updatedConfig : config
		));

		setSnackbar({
			open: true,
			message: 'Test case batch added successfully',
			severity: 'success',
		});

		handleBatchClose();
	};

	const handleBatchDelete = (configId, batchId) => {
		if (window.confirm('Are you sure you want to delete this batch?')) {
			const config = buildConfigs.find(c => c.id === configId);
			const updatedConfig = {
				...config,
				batches: (config.batches || []).filter(batch => batch.id !== batchId),
				updatedAt: new Date().toISOString(),
			};

			setBuildConfigs(buildConfigs.map(c =>
				c.id === configId ? updatedConfig : c
			));

			setSnackbar({
				open: true,
				message: 'Batch deleted successfully',
				severity: 'success',
			});
		}
	};

	const getBuildConfigName = (configId) => {
		const config = buildConfigs.find(c => c.id === configId);
		return config ? config.name : 'Unknown Config';
	};

	const getTagColor = (tag) => {
		const colors = {
			'release': 'success',
			'pr': 'warning',
			'nightly': 'info',
		};
		return colors[tag] || 'default';
	};

	const formatJSON = (jsonString) => {
		if (!jsonString) return 'No data';
		try {
			const parsed = JSON.parse(jsonString);
			return JSON.stringify(parsed, null, 2);
		} catch {
			return jsonString;
		}
	};

	const TabPanel = ({ children, value, index }) => (
		<div hidden={value !== index}>
			{value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
		</div>
	);

	return (
		<Box sx={{p: 3}}>
			<Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
				<Typography variant="h4">Testing Plan</Typography>
				{/* <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add Bconfig
        </Button> */}
			</Box>

			{/* Build Configuration Section */}
			<Paper sx={{ p: 3, mb: 3 }}>
				<Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
					<Typography variant="h6">
						Total Bconfig: {buildConfigs.length}
					</Typography>
					<Button
						variant="outlined"
						startIcon={<AddIcon />}
						onClick={() => handleBconfigOpen()}
					>
						Add Bconfig
					</Button>
				</Box>

				{buildConfigs.length === 0 ? (
					<Typography variant="body2" color="textSecondary" textAlign="center" py={4}>
						No Build Configurations found. Click "Add Bconfig" to create one.
					</Typography>
				) : (
					buildConfigs.map((config) => (
						<Accordion key={config.id} sx={{ mb: 1 }}>
							<AccordionSummary
								expandIcon={<ExpandMoreIcon />}
								aria-controls={`panel-${config.id}-content`}
								id={`panel-${config.id}-header`}
							>
								<Box display="flex" alignItems="center" gap={2} width="100%">
									<Typography variant="h6" component="div">
										{config.name}
									</Typography>
									<Chip
										label={config.status}
										color={config.status === 'Active' ? 'success' : 'default'}
										size="small"
									/>
									<Chip
										label={config.tag}
										color={getTagColor(config.tag)}
										size="small"
									/>
									<Box sx={{ ml: 'auto' }}>
										<IconButton
											size="small"
											onClick={(e) => {
												e.stopPropagation();
												handleBconfigOpen(config);
											}}
											color="primary"
										>
											<EditIcon />
										</IconButton>
										<IconButton
											size="small"
											onClick={(e) => {
												e.stopPropagation();
												handleBconfigDelete(config.id);
											}}
											color="error"
										>
											<DeleteIcon />
										</IconButton>
									</Box>
								</Box>
							</AccordionSummary>
							<AccordionDetails>
								<Grid container spacing={2}>
									<Grid size={{ xs: 12, md: 6 }}>
										<Typography variant="subtitle2" gutterBottom>
											Description:
										</Typography>
										<Typography variant="body2" color="textSecondary">
											{typeof (config.description) === 'object' ? JSON.stringify(config.description) : config.description || 'No description provided'}
										</Typography>
									</Grid>
									<Grid size={{ xs: 12, md: 6 }}>
										<Typography variant="subtitle2" gutterBottom>
											Test Cases:
										</Typography>
										<Typography variant="body2" color="textSecondary">
											{config.testcases?.length || 0} test cases selected
										</Typography>
									</Grid>
									<Grid size={{ xs: 12, md: 6 }}>
										<Typography variant="subtitle2" gutterBottom>
											Board Information:
										</Typography>
										<Typography variant="body2" color="textSecondary">
											{boardInfos.find(b => b.id === config.boardInformationId)?.name || 'Not selected'}
										</Typography>
									</Grid>
									<Grid size={{ xs: 12, md: 6 }}>
										<Typography variant="subtitle2" gutterBottom>
											Created:
										</Typography>
										<Typography variant="body2" color="textSecondary">
											{new Date(config.createdAt).toLocaleDateString()}
										</Typography>
									</Grid>

									{/* Test Case Batches Section */}
									<Grid size={{ xs: 12 }}>
										<Box display="flex" justifyContent="space-between" alignItems="center" mt={2} mb={1}>
											<Typography variant="subtitle2">
												Test Case Batches ({(config.batches || []).length})
											</Typography>
											<Button
												variant="outlined"
												size="small"
												startIcon={<AddIcon />}
												onClick={() => handleBatchOpen(config)}
											>
												Add Testcase batch
											</Button>
										</Box>

										{(config.batches || []).length === 0 ? (
											<Typography variant="body2" color="textSecondary" textAlign="center" py={2}>
												No test case batches. Click "Add Testcase batch" to create one.
											</Typography>
										) : (
											<Box>
												{config.batches.map((batch) => (
													<Paper key={batch.id} sx={{ p: 2, mb: 1, bgcolor: 'grey.50' }}>
														<Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
															<Typography variant="subtitle2" fontWeight="bold">
																{batch.name}
															</Typography>
															<Box display="flex" gap={1}>
																<IconButton
																	size="small"
																	onClick={() => handleBatchDelete(config.id, batch.id)}
																	color="error"
																>
																	<DeleteIcon />
																</IconButton>
															</Box>
														</Box>
														<Typography variant="body2">
															<strong>Test Cases:</strong> {batch.testcases.length} selected
														</Typography>
														<Box mt={1}>
															{batch.testcases.map(tcId => {
																const tc = testCases.find(t => t.id === tcId);
																return tc ? (
																	<Chip
																		key={tcId}
																		label={tc.testcase_id}
																		size="small"
																		sx={{ mr: 0.5, mb: 0.5 }}
																	/>
																) : null;
															})}
														</Box>
													</Paper>
												))}
											</Box>
										)}
									</Grid>
								</Grid>
							</AccordionDetails>
						</Accordion>
					))
				)}
			</Paper>

			{/* Test Case Batch Dialog */}
			<Dialog open={batchOpen} onClose={handleBatchClose} maxWidth="md" fullWidth>
				<DialogTitle>
					Add Test Case Batch to "{currentBconfig?.name}"
				</DialogTitle>
				<DialogContent>
					<TextField
						autoFocus
						margin="dense"
						label="Batch Name"
						fullWidth
						variant="outlined"
						value={batchFormData.name}
						onChange={(e) => setBatchFormData({ ...batchFormData, name: e.target.value })}
						sx={{ mb: 2 }}
						required
					/>
					<FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
						<InputLabel>Test Cases</InputLabel>
						<Select
							multiple
							value={batchFormData.testcases}
							onChange={(e) => setBatchFormData({ ...batchFormData, testcases: e.target.value })}
							input={<OutlinedInput label="Test Cases" />}
							renderValue={(selected) => {
								return selected.map(id => {
									const testcase = testCases.find(tc => tc.id === id);
									return testcase ? testcase.testcase_id : 'Unknown';
								}).join(', ');
							}}
							MenuProps={MenuProps}
						>
							{testCases.map((testcase) => (
								<MenuItem key={testcase.id} value={testcase.id}>
									<Checkbox checked={batchFormData.testcases.indexOf(testcase.id) > -1} />
									<ListItemText
										primary={testcase.testcase_id}
										secondary={testcase.brief}
									/>
								</MenuItem>
							))}
						</Select>
					</FormControl>

					{batchFormData.testcases.length > 0 && (
						<Box>
							<Typography variant="subtitle2" gutterBottom>
								Selected Test Cases ({batchFormData.testcases.length}):
							</Typography>
							<Box>
								{batchFormData.testcases.map(tcId => {
									const tc = testCases.find(t => t.id === tcId);
									return tc ? (
										<Chip
											key={tcId}
											label={tc.testcase_id}
											size="small"
											sx={{ mr: 0.5, mb: 0.5 }}
											onDelete={() => {
												setBatchFormData({
													...batchFormData,
													testcases: batchFormData.testcases.filter(id => id !== tcId)
												});
											}}
										/>
									) : null;
								})}
							</Box>
						</Box>
					)}
				</DialogContent>
				<DialogActions>
					<Button onClick={handleBatchClose}>Cancel</Button>
					<Button
						onClick={handleBatchSubmit}
						variant="contained"
						disabled={!batchFormData.name.trim() || batchFormData.testcases.length === 0}
					>
						Add Batch
					</Button>
				</DialogActions>
			</Dialog>

			{/* Build Configuration Dialog */}
			<Dialog open={bconfigOpen} onClose={handleBconfigClose} maxWidth="md" fullWidth>
				<DialogTitle>
					{editingBconfig ? 'Edit Build Configuration' : 'Create New Build Configuration'}
				</DialogTitle>
				<DialogContent>
					<TextField
						autoFocus
						margin="dense"
						label="Name"
						fullWidth
						variant="outlined"
						value={bconfigFormData.name}
						onChange={(e) => setBconfigFormData({ ...bconfigFormData, name: e.target.value })}
						sx={{ mb: 2 }}
						required
					/>

					<Grid container spacing={2} sx={{ mb: 2 }}>
						<Grid size={{ xs: 12, md: 6 }}>
							<TextField
								label="Status"
								sx={{ width: '100%' }}
								slotProps={{
									input: {
										startAdornment: <InputAdornment position="end">%</InputAdornment>,
									},
								}}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6 }}>
							<FormControl fullWidth variant="outlined">
								<InputLabel>Tag</InputLabel>
								<Select
									value={bconfigFormData.tag}
									onChange={(e) => setBconfigFormData({ ...bconfigFormData, tag: e.target.value })}
									label="Tag"
								>
									{tags.map((tag) => (
										<MenuItem key={tag} value={tag}>
											{tag}
										</MenuItem>
									))}
								</Select>
							</FormControl>
						</Grid>
					</Grid>

					<TextField
						margin="dense"
						label="Description"
						fullWidth
						multiline
						rows={3}
						variant="outlined"
						value={bconfigFormData.description}
						onChange={(e) => setBconfigFormData({ ...bconfigFormData, description: e.target.value })}
						sx={{ mb: 2 }}
					/>

					<FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
						<InputLabel>Test Cases</InputLabel>
						<Select
							multiple
							value={bconfigFormData.testcases}
							onChange={(e) => setBconfigFormData({ ...bconfigFormData, testcases: e.target.value })}
							input={<OutlinedInput label="Test Cases" />}
							renderValue={(selected) => {
								return selected.map(id => {
									const testcase = testCases.find(tc => tc.id === id);
									return testcase ? testcase.testcase_id : 'Unknown';
								}).join(', ');
							}}
							MenuProps={MenuProps}
						>
							{testCases.map((testcase) => (
								<MenuItem key={testcase.id} value={testcase.id}>
									<Checkbox checked={bconfigFormData.testcases.indexOf(testcase.id) > -1} />
									<ListItemText
										primary={testcase.testcase_id}
										secondary={testcase.brief}
									/>
								</MenuItem>
							))}
						</Select>
					</FormControl>

					<FormControl fullWidth variant="outlined">
						<InputLabel>Board Information</InputLabel>
						<Select
							value={bconfigFormData.boardInformationId}
							onChange={(e) => setBconfigFormData({ ...bconfigFormData, boardInformationId: e.target.value })}
							label="Board Information"
						>
							<MenuItem value="">
								<em>None</em>
							</MenuItem>
							{boardInfos.map((board) => (
								<MenuItem key={board.id} value={board.id}>
									{board.name}
								</MenuItem>
							))}
						</Select>
					</FormControl>
				</DialogContent>
				<DialogActions>
					<Button onClick={handleBconfigClose}>Cancel</Button>
					<Button
						onClick={handleBconfigSubmit}
						variant="contained"
						disabled={!bconfigFormData.name.trim()}
					>
						{editingBconfig ? 'Update' : 'Create'}
					</Button>
				</DialogActions>
			</Dialog>

			<Snackbar
				open={snackbar.open}
				autoHideDuration={6000}
				onClose={() => setSnackbar({ ...snackbar, open: false })}
			>
				<Alert
					onClose={() => setSnackbar({ ...snackbar, open: false })}
					severity={snackbar.severity}
					sx={{ width: '100%' }}
				>
					{snackbar.message}
				</Alert>
			</Snackbar>
			<Stack spacing={2}>
				{/* <Pagination count={10} shape="rounded" /> */}
				<Pagination count={100} variant="outlined" color="primary" shape="rounded" />
			</Stack>
		</Box>
	);
};

export default TestingPlan;
