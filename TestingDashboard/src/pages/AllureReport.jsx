import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Autocomplete,
    TextField,
    Paper,
    CircularProgress,
    Alert,
    Chip,
    Card,
    CardContent,
} from '@mui/material';
import AssessmentIcon from '@mui/icons-material/Assessment';
import allureReportService from '../services/allureReportService';

const AllureReport = () => {
    const [allureReports, setAllureReports] = useState([]);
    const [selectedReport, setSelectedReport] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [iframeLoading, setIframeLoading] = useState(false);

    // Load all allure reports on component mount
    useEffect(() => {
        loadAllureReports();
    }, []);

    const loadAllureReports = async () => {
        try {
            setLoading(true);
            setError(null);
            console.log('Loading allure reports...');

            const reports = await allureReportService.getAllureReports();
            const reportList = reports.results || reports || [];

            console.log(`Loaded ${reportList.length} allure reports`);
            setAllureReports(reportList);
        } catch (error) {
            console.error('Error loading allure reports:', error);
            setError('Failed to load allure reports. Please try again.');
            setAllureReports([]);
        } finally {
            setLoading(false);
        }
    };

    const handleReportSelection = (event, newValue) => {
        console.log('Selected report:', newValue);
        setSelectedReport(newValue);
        if (newValue) {
            setIframeLoading(true);
        }
    };

    const handleIframeLoad = () => {
        console.log('Iframe loaded successfully');
        setIframeLoading(false);
    };

    const handleIframeError = () => {
        console.log('Iframe failed to load');
        setIframeLoading(false);
    };

    return (
        <Box sx={{ p: 3 }}>
            {/* Header */}
            {/* <Typography variant="h4" component="h1" gutterBottom>
                Allure Reports
            </Typography> */}

            {/* <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Select an allure report to view in the iframe below.
            </Typography> */}

            {/* Error Alert */}
            {error && (
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Report Selection */}
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    {/* <Typography variant="h6" gutterBottom>
                        Select Report
                    </Typography> */}

                    <Autocomplete
                        options={allureReports}
                        value={selectedReport}
                        onChange={handleReportSelection}
                        loading={loading}
                        getOptionLabel={(option) =>
                            `${option.testing_plan_total_name} - ${new Date(option.created_at).toLocaleDateString()}`
                        }
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        renderOption={(props, option) => (
                            <Box component="li" {...props}>
                                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                                    <Typography variant="body1">
                                        {option.testing_plan_total_name}
                                    </Typography>
                                    <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                                        <Chip
                                            label={option.testing_plan_total_type}
                                            size="small"
                                            color="primary"
                                            variant="outlined"
                                        />
                                        <Chip
                                            label={new Date(option.created_at).toLocaleDateString()}
                                            size="small"
                                            color="secondary"
                                            variant="outlined"
                                        />
                                    </Box>
                                </Box>
                            </Box>
                        )}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                label="Choose Allure Report"
                                placeholder="Type to search reports..."
                                variant="outlined"
                                slotProps={{
                                    input: {
                                        ...params.InputProps,
                                        endAdornment: (
                                            <>
                                                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                                                {params.InputProps.endAdornment}
                                            </>
                                        ),
                                    },
                                }}
                            />
                        )}
                        sx={{ mb: 2 }}
                    />

                    {selectedReport && (
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                            <Chip
                                label={`Testing Plan: ${selectedReport.testing_plan_total_name}`}
                                color="primary"
                            />
                            <Chip
                                label={`Type: ${selectedReport.testing_plan_total_type}`}
                                color="secondary"
                            />
                            <Chip
                                label={`Created: ${new Date(selectedReport.created_at).toLocaleString()}`}
                                variant="outlined"
                            />
                        </Box>
                    )}
                </CardContent>
            </Card>

            {/* Iframe Container */}
            {selectedReport && (
                <Card>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6">
                                Report Viewer
                            </Typography>
                            {iframeLoading && (
                                <CircularProgress size={20} sx={{ ml: 2 }} />
                            )}
                        </Box>

                        <Paper
                            elevation={3}
                            sx={{
                                position: 'relative',
                                height: '70vh',
                                overflow: 'hidden'
                            }}
                        >
                            {iframeLoading && (
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        bottom: 0,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                        zIndex: 1
                                    }}
                                >
                                    <CircularProgress />
                                </Box>
                            )}

                            <iframe
                                src={'/allure-report/index.html'}
                                // src={selectedReport.report_file}
                                title={`Allure Report - ${selectedReport.testing_plan_total_name}`}
                                width="100%"
                                height="100%"
                                frameBorder="0"
                                onLoad={handleIframeLoad}
                                onError={handleIframeError}
                                style={{
                                    border: 'none',
                                    display: 'block'
                                }}
                            />
                        </Paper>
                    </CardContent>
                </Card>
            )}

            {/* No Report Selected */}
            {!selectedReport && !loading && (
                <Card>
                    <CardContent sx={{ textAlign: 'center', py: 6 }}>
                        <AssessmentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                            No Report Selected
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Please select an allure report from the dropdown above to view it.
                        </Typography>
                    </CardContent>
                </Card>
            )}
        </Box>
    );
};

export default AllureReport;
