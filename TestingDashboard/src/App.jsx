import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import theme from './theme';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';
import MainLayout from './components/Layout/MainLayout';
import ErrorBoundary from './components/ErrorBoundary';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import TestCases from './pages/TestCases';
import BoardSetup from './pages/BoardSetup';
import BoardInformation from './pages/BoardInformation';
import BoardHW from './pages/BoardHW';
import BoardTopology from './pages/BoardTopology';
import BuildConfiguration from './pages/BuildConfiguration';
import BoardConfiguration from './pages/BoardConfiguration';
import TestingPlan from './pages/TestingPlan';
import TestingPlanDashboard from './pages/TestingPlanDashboard';
import TestingPlanDetail from './pages/TestingPlanDetail';
import UserManagement from './pages/UserManagement';
import ApiDemo from './pages/ApiDemo';
import AllureReport from './pages/AllureReport';

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <Dashboard />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/testcases"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <TestCases />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/boards"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <BoardSetup />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/board-information"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <BoardInformation />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/board-hw"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <BoardHW />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/board-topology"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <BoardTopology />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/build-config"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <BuildConfiguration />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/board-config"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <BoardConfiguration />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/testing-plan"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <TestingPlan />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/testing-plan-dashboard"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <TestingPlanDashboard />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/testing-plan-detail/:id"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <TestingPlanDetail />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/users"
                element={
                  <ProtectedRoute>
                    <AdminRoute>
                      <MainLayout>
                        <UserManagement />
                      </MainLayout>
                    </AdminRoute>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/allure-report"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <AllureReport />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
            </Routes>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
