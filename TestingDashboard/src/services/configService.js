import apiService from './apiService';

class ConfigService {
    constructor() {
        this.baseEndpoint = '/api/config';
    }

    // Build Configuration APIs
    async getBuildConfigurations(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/build-configurations/`, filters);
    }

    async getBuildConfigurationById(id) {
        return apiService.get(`${this.baseEndpoint}/build-configurations/${id}/`);
    }

    async createBuildConfiguration(data) {
        return apiService.post(`${this.baseEndpoint}/build-configurations/`, data);
    }

    async updateBuildConfiguration(id, data) {
        return apiService.put(`${this.baseEndpoint}/build-configurations/${id}/`, data);
    }

    async patchBuildConfiguration(id, data) {
        return apiService.patch(`${this.baseEndpoint}/build-configurations/${id}/`, data);
    }

    async deleteBuildConfiguration(id) {
        return apiService.delete(`${this.baseEndpoint}/build-configurations/${id}/`);
    }

    // Board Configuration APIs
    async getBoardConfigurations(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-configurations/`, filters);
    }

    async getBoardConfigurationById(id) {
        return apiService.get(`${this.baseEndpoint}/board-configurations/${id}/`);
    }

    async createBoardConfiguration(data) {
        return apiService.post(`${this.baseEndpoint}/board-configurations/`, data);
    }

    async updateBoardConfiguration(id, data) {
        return apiService.put(`${this.baseEndpoint}/board-configurations/${id}/`, data);
    }

    async patchBoardConfiguration(id, data) {
        return apiService.patch(`${this.baseEndpoint}/board-configurations/${id}/`, data);
    }

    async deleteBoardConfiguration(id) {
        return apiService.delete(`${this.baseEndpoint}/board-configurations/${id}/`);
    }

    // Get choices for dropdowns
    async getChoices() {
        return apiService.get(`${this.baseEndpoint}/choices/`);
    }

    // Search functions
    async searchBuildConfigurations(searchTerm, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/build-configurations/`, {
            search: searchTerm,
            ...filters
        });
    }

    async searchBoardConfigurations(searchTerm, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-configurations/`, {
            search: searchTerm,
            ...filters
        });
    }

    // Filter by board information
    async getBuildConfigurationsByBoardInfo(boardInfoId) {
        return apiService.get(`${this.baseEndpoint}/build-configurations/`, {
            board_information: boardInfoId
        });
    }

    async getBoardConfigurationsByBoardInfo(boardInfoId) {
        return apiService.get(`${this.baseEndpoint}/board-configurations/`, {
            board_information: boardInfoId
        });
    }

    // Filter by status
    async getBuildConfigurationsByStatus(status) {
        return apiService.get(`${this.baseEndpoint}/build-configurations/`, { status });
    }

    async getBoardConfigurationsByStatus(status) {
        return apiService.get(`${this.baseEndpoint}/board-configurations/`, { status });
    }

    // Filter by priority
    async getBuildConfigurationsByPriority(priority) {
        return apiService.get(`${this.baseEndpoint}/build-configurations/`, { priority });
    }

    async getBoardConfigurationsByPriority(priority) {
        return apiService.get(`${this.baseEndpoint}/board-configurations/`, { priority });
    }

    // Bulk operations
    async bulkUpdateBuildConfigStatus(configIds, status) {
        const promises = configIds.map(id => 
            this.patchBuildConfiguration(id, { status })
        );
        return Promise.all(promises);
    }

    async bulkUpdateBoardConfigStatus(configIds, status) {
        const promises = configIds.map(id => 
            this.patchBoardConfiguration(id, { status })
        );
        return Promise.all(promises);
    }

    // Validation helpers
    validateBuildConfiguration(data) {
        const errors = {};

        if (!data.name?.trim()) {
            errors.name = 'Configuration name is required';
        }

        if (!data.board_information) {
            errors.board_information = 'Board Information is required';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    validateBoardConfiguration(data) {
        const errors = {};

        if (!data.name?.trim()) {
            errors.name = 'Configuration name is required';
        }

        if (!data.board_information) {
            errors.board_information = 'Board Information is required';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
}

export default new ConfigService();
