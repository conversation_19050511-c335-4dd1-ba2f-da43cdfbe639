import axios from 'axios';

class JenkinsService {
	constructor() {
		this.apiURL = import.meta.env.VITE_API_URL || 'http://localhost:8080';
		this.baseURL = import.meta.env.VITE_JENKINS_URL || 'http://localhost:8080';
		this.username = import.meta.env.VITEP_JENKINS_USERNAME || 'admin';
		this.token = import.meta.env.VITE_JENKINS_TOKEN || 'dashboard_ci';
		this.buildJob = import.meta.env.VITE_JENKINS_BUILD_JOB || 'Build';
	}

	async triggerBuild(jobName, parameters = {}) {
		try {
			parameters["token"] = this.token
			parameters["dashboard_api"] = this.apiURL
			const queryString = new URLSearchParams(parameters).toString();
		    console.log("triggerJenkinsBuild")
		    console.log(`${this.baseURL}/job/${jobName}/buildWithParameters?${queryString}`)
			const response = await fetch(`${this.baseURL}/job/${jobName}/buildWithParameters?${queryString}`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
			})
			if (response.ok) {
				return {
					success: true,
					buildNumber,
					message: `Build #${buildNumber} triggered successfully`,
					queueUrl: `${this.baseURL}/queue/item/${buildNumber}/`
				}
			} else {
				console.error('Error triggering Jenkins build:', error);
				return {
					success: false,
					error: error.message || 'Failed to trigger build',
				};
			}
		} catch (error) {
			console.error('Error triggering Jenkins build:', error);
			return {
				success: false,
				error: error.message || 'Failed to trigger build',
			};
		}
	}

	async getBuildStatus(jobName, buildNumber) {
		try {
		// Simulate getting build status
		await new Promise(resolve => setTimeout(resolve, 500));

		const statuses = ['SUCCESS', 'FAILURE', 'UNSTABLE', 'ABORTED', 'IN_PROGRESS'];
		const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

		return {
			success: true,
			status: randomStatus,
			duration: Math.floor(Math.random() * 300000), // Random duration in ms
			timestamp: new Date().toISOString(),
		};
		} catch (error) {
		console.error('Error getting build status:', error);
		return {
			success: false,
			error: error.message || 'Failed to get build status',
		};
		}
	}

	async getJobList() {
		try {
		// Simulate getting job list
		await new Promise(resolve => setTimeout(resolve, 500));

		return {
			success: true,
			jobs: [
			'yocto-build-arm64',
			'yocto-build-x86_64',
			'yocto-build-arm32',
			'yocto-build-riscv',
			],
		};
		} catch (error) {
		console.error('Error getting job list:', error);
		return {
			success: false,
			error: error.message || 'Failed to get job list',
		};
		}
	}
}

export default new JenkinsService();
