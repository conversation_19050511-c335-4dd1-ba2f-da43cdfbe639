import apiService from './apiService';

class TestingPlanService {
    constructor() {
        this.baseEndpoint = '/api/testing-plan';
    }

    // Testing Plan APIs
    async getTestingPlans(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/testing-plans/`, filters);
    }

    async getTestingPlanById(id) {
        return apiService.get(`${this.baseEndpoint}/testing-plans/${id}/`);
    }

    async createTestingPlan(data) {
        return apiService.post(`${this.baseEndpoint}/testing-plans/`, data);
    }

    async updateTestingPlan(id, data) {
        return apiService.put(`${this.baseEndpoint}/testing-plans/${id}/`, data);
    }

    async patchTestingPlan(id, data) {
        return apiService.patch(`${this.baseEndpoint}/testing-plans/${id}/`, data);
    }

    async deleteTestingPlan(id) {
        return apiService.delete(`${this.baseEndpoint}/testing-plans/${id}/`);
    }

    // Testing Plan Batch APIs
    async getTestingPlanBatches(testingPlanId, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/testing-plans/${testingPlanId}/batches/`, filters);
    }

    async getTestingPlanBatchById(testingPlanId, batchId) {
        return apiService.get(`${this.baseEndpoint}/testing-plans/${testingPlanId}/batches/${batchId}/`);
    }

    async createTestingPlanBatch(testingPlanId, data) {
        return apiService.post(`${this.baseEndpoint}/testing-plans/${testingPlanId}/batches/`, data);
    }

    async updateTestingPlanBatch(testingPlanId, batchId, data) {
        return apiService.put(`${this.baseEndpoint}/testing-plans/${testingPlanId}/batches/${batchId}/`, data);
    }

    async patchTestingPlanBatch(testingPlanId, batchId, data) {
        return apiService.patch(`${this.baseEndpoint}/testing-plans/${testingPlanId}/batches/${batchId}/`, data);
    }

    async deleteTestingPlanBatch(testingPlanId, batchId) {
        return apiService.delete(`${this.baseEndpoint}/testing-plans/${testingPlanId}/batches/${batchId}/`);
    }

    // Jenkins Integration
    async triggerJenkinsBuild(testingPlanId, jobName = 'default_job') {
        return apiService.post(`${this.baseEndpoint}/testing-plans/${testingPlanId}/trigger-jenkins/`, {
            job_name: jobName
        });
    }

    // Get choices for dropdowns
    async getChoices() {
        return apiService.get(`${this.baseEndpoint}/choices/`);
    }

    // Search functions
    async searchTestingPlans(searchTerm, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/testing-plans/`, {
            search: searchTerm,
            ...filters
        });
    }

    // Filter by status
    async getTestingPlansByStatus(status) {
        return apiService.get(`${this.baseEndpoint}/testing-plans/`, { status });
    }

    // Filter by priority
    async getTestingPlansByPriority(priority) {
        return apiService.get(`${this.baseEndpoint}/testing-plans/`, { priority });
    }

    // Bulk operations
    async bulkUpdateStatus(planIds, status) {
        const promises = planIds.map(id => 
            this.patchTestingPlan(id, { status })
        );
        return Promise.all(promises);
    }

    async bulkTriggerJenkins(planIds, jobName = 'default_job') {
        const promises = planIds.map(id => 
            this.triggerJenkinsBuild(id, jobName)
        );
        return Promise.all(promises);
    }

    // Validation helpers
    validateTestingPlan(data) {
        const errors = {};

        if (!data.name?.trim()) {
            errors.name = 'Testing Plan name is required';
        }

        if (!data.description?.trim()) {
            errors.description = 'Description is required';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    validateTestingPlanBatch(data) {
        const errors = {};

        if (!data.name?.trim()) {
            errors.name = 'Batch name is required';
        }

        if (!data.description?.trim()) {
            errors.description = 'Description is required';
        }

        if (data.order !== undefined && data.order < 0) {
            errors.order = 'Order must be a positive number';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    // Helper methods for managing test cases in plans
    async addTestCasesToPlan(planId, testCaseIds) {
        const plan = await this.getTestingPlanById(planId);
        const updatedTestCases = [...new Set([...plan.test_cases, ...testCaseIds])];
        return this.patchTestingPlan(planId, { test_cases: updatedTestCases });
    }

    async removeTestCasesFromPlan(planId, testCaseIds) {
        const plan = await this.getTestingPlanById(planId);
        const updatedTestCases = plan.test_cases.filter(id => !testCaseIds.includes(id));
        return this.patchTestingPlan(planId, { test_cases: updatedTestCases });
    }

    // Helper methods for managing configurations in plans
    async addBuildConfigurationsToPlan(planId, configIds) {
        const plan = await this.getTestingPlanById(planId);
        const updatedConfigs = [...new Set([...plan.build_configurations, ...configIds])];
        return this.patchTestingPlan(planId, { build_configurations: updatedConfigs });
    }

    async addBoardConfigurationsToPlan(planId, configIds) {
        const plan = await this.getTestingPlanById(planId);
        const updatedConfigs = [...new Set([...plan.board_configurations, ...configIds])];
        return this.patchTestingPlan(planId, { board_configurations: updatedConfigs });
    }
}

export default new TestingPlanService();
