import apiService from './apiService';
import jenkinsService from './jenkinsService';

const testingPlanTotalService = {
  // Testing Plan Total APIs
  getTestingPlanTotals: async (params = {}) => {
    const response = await apiService.get('/api/testing-plan/testing-plan-totals/', params);
    return response;
  },

  createTestingPlanTotal: async (data) => {
    const response = await apiService.post('/api/testing-plan/testing-plan-totals/', data);
    return response.data;
  },

  getTestingPlanTotal: async (id) => {
    const response = await apiService.get(`/api/testing-plan/testing-plan-totals/${id}/`);
    return response;
  },

  updateTestingPlanTotal: async (id, data) => {
    const response = await apiService.put(`/api/testing-plan/testing-plan-totals/${id}/`, data);
    return response.data;
  },

  deleteTestingPlanTotal: async (id) => {
    const response = await apiService.delete(`/api/testing-plan/testing-plan-totals/${id}/`);
    return response.data;
  },

  triggerJenkinsBuildTotal: async (id, data = {}) => {
    console.log("triggerJenkinsBuildTotal")
    const response = await jenkinsService.triggerBuild(jenkinsService.buildJob, {
      linux_branch: "main",
      auto_br_hash: "main",
      board_type: "TDA4VM-SK"
    })
    return response.data;
  },

  // Testing Plan APIs
  getTestingPlans: async (params = {}) => {
    const response = await apiService.get('/api/testing-plan/testing-plans/', params);
    return response;
  },

  createTestingPlan: async (data) => {
    const response = await apiService.post('/api/testing-plan/testing-plans/', data);
    return response.data;
  },

  getTestingPlan: async (id) => {
    const response = await apiService.get(`/api/testing-plan/testing-plans/${id}/`);
    return response;
  },

  updateTestingPlan: async (id, data) => {
    const response = await apiService.put(`/api/testing-plan/testing-plans/${id}/`, data);
    return response.data;
  },

  deleteTestingPlan: async (id) => {
    const response = await apiService.delete(`/api/testing-plan/testing-plans/${id}/`);
    return response.data;
  },

  triggerJenkinsBuild: async (id, data = {}) => {
    // const response = await apiService.post(`/api/testing-plan/testing-plans/${id}/trigger-jenkins/`, data);
    // return response.data;
    console.log("triggerJenkinsBuild")
    const response = await jenkinsService.triggerBuild(jenkinsService.buildJob, {
      testing_plan_id: id.toString(),
      linux_branch: "main",
      auto_br_hash: "main",
      board_type: "TDA4VM-SK"
    })
    return response.data;
  },

  // Testing Plan Batch APIs
  getTestingPlanBatches: async (testingPlanId) => {
    const response = await apiService.get(`/api/testing-plan/testing-plans/${testingPlanId}/batches/`);
    return response;
  },

  createTestingPlanBatch: async (testingPlanId, data) => {
    const response = await apiService.post(`/api/testing-plan/testing-plans/${testingPlanId}/batches/`, data);
    return response.data;
  },

  getTestingPlanBatch: async (testingPlanId, batchId) => {
    const response = await apiService.get(`/api/testing-plan/testing-plans/${testingPlanId}/batches/${batchId}/`);
    return response;
  },

  updateTestingPlanBatch: async (testingPlanId, batchId, data) => {
    const response = await apiService.put(`/api/testing-plan/testing-plans/${testingPlanId}/batches/${batchId}/`, data);
    return response.data;
  },

  deleteTestingPlanBatch: async (testingPlanId, batchId) => {
    const response = await apiService.delete(`/api/testing-plan/testing-plans/${testingPlanId}/batches/${batchId}/`);
    return response.data;
  },

  // Testing Plan Run APIs
  getTestingPlanRuns: async (batchId) => {
    const response = await apiService.get(`/api/testing-plan/batches/${batchId}/runs/`);
    return response;
  },

  createTestingPlanRun: async (batchId, data) => {
    const response = await apiService.post(`/api/testing-plan/batches/${batchId}/runs/`, data);
    return response.data;
  },

  getTestingPlanRun: async (batchId, runId) => {
    const response = await apiService.get(`/api/testing-plan/batches/${batchId}/runs/${runId}/`);
    return response;
  },

  updateTestingPlanRun: async (batchId, runId, data) => {
    const response = await apiService.put(`/api/testing-plan/batches/${batchId}/runs/${runId}/`, data);
    return response.data;
  },

  deleteTestingPlanRun: async (batchId, runId) => {
    const response = await apiService.delete(`/api/testing-plan/batches/${batchId}/runs/${runId}/`);
    return response.data;
  },

  // Result APIs
  getResults: async (runId) => {
    const response = await apiService.get(`/api/testing-plan/runs/${runId}/results/`);
    return response;
  },

  createResult: async (runId, data) => {
    const response = await apiService.post(`/api/testing-plan/runs/${runId}/results/`, data);
    return response.data;
  },

  getResult: async (runId, resultId) => {
    const response = await apiService.get(`/api/testing-plan/runs/${runId}/results/${resultId}/`);
    return response;
  },

  updateResult: async (runId, resultId, data) => {
    const response = await apiService.put(`/api/testing-plan/runs/${runId}/results/${resultId}/`, data);
    return response.data;
  },

  deleteResult: async (runId, resultId) => {
    const response = await apiService.delete(`/api/testing-plan/runs/${runId}/results/${resultId}/`);
    return response.data;
  },

  // Choices API
  getChoices: async () => {
    const response = await apiService.get('/api/testing-plan/choices/');
    return response;
  }
};

export default testingPlanTotalService;
