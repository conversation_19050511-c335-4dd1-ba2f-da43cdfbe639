import apiService from './apiService';

class AllureReportService {
    constructor() {
        this.baseEndpoint = '/api/testing-plan';
    }

    // Get all allure reports with optional filters (no pagination, sorted by created_at desc)
    async getAllureReports(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/allure-reports/`, {
            ...filters,
            all: 'true',  // Get all reports without pagination
            ordering: '-created_at'  // Sort by created_at descending (newest first)
        });
    }

    // Get a specific allure report by ID
    async getAllureReport(id) {
        return apiService.get(`${this.baseEndpoint}/allure-reports/${id}/`);
    }

    // Create a new allure report
    async createAllureReport(data) {
        return apiService.post(`${this.baseEndpoint}/allure-reports/`, data);
    }

    // Update an allure report
    async updateAllureReport(id, data) {
        return apiService.put(`${this.baseEndpoint}/allure-reports/${id}/`, data);
    }

    // Delete an allure report
    async deleteAllureReport(id) {
        return apiService.delete(`${this.baseEndpoint}/allure-reports/${id}/`);
    }

    // Get 10 most recent allure reports for sidebar
    async getRecentAllureReports() {
        return apiService.get(`${this.baseEndpoint}/allure-reports/recent/`);
    }

    // Get allure reports by testing plan total
    async getAllureReportsByTestingPlanTotal(testingPlanTotalId) {
        return apiService.get(`${this.baseEndpoint}/allure-reports/`, {
            testing_plan_total: testingPlanTotalId
        });
    }
}

export default new AllureReportService();
