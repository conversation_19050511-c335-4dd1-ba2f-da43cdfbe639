import apiService from './apiService';

class TestCaseService {
    constructor() {
        this.baseEndpoint = '/api/testcase';
    }

    // Get all test cases with optional filters
    async getTestCases(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/testcases/`, filters);
    }

    // Get a specific test case by ID
    async getTestCase(id) {
        return apiService.get(`${this.baseEndpoint}/testcases/${id}/`);
    }

    // Create a new test case
    async createTestCase(testCaseData) {
        console.log(testCaseData);
        return apiService.post(`${this.baseEndpoint}/testcases/`, testCaseData);
    }

    // Update an existing test case
    async updateTestCase(id, testCaseData) {
        return apiService.put(`${this.baseEndpoint}/testcases/${id}/`, testCaseData);
    }

    // Partially update a test case
    async patchTestCase(id, testCaseData) {
        return apiService.patch(`${this.baseEndpoint}/testcases/${id}/`, testCaseData);
    }

    // Delete a test case
    async deleteTestCase(id) {
        return apiService.delete(`${this.baseEndpoint}/testcases/${id}/`);
    }

    // Get choices for dropdowns
    async getChoices() {
        return apiService.get(`${this.baseEndpoint}/testcases/choices/`);
    }

    // Search test cases
    async searchTestCases(searchTerm, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/testcases/`, {
            search: searchTerm,
            ...filters
        });
    }

    // Get test cases by status
    async getTestCasesByStatus(status) {
        return apiService.get(`${this.baseEndpoint}/testcases/`, { status });
    }

    // Get test cases by priority
    async getTestCasesByPriority(priority) {
        return apiService.get(`${this.baseEndpoint}/testcases/`, { priority });
    }

    // Get test cases by test level
    async getTestCasesByTestLevel(testLevel) {
        return apiService.get(`${this.baseEndpoint}/testcases/`, { test_level: testLevel });
    }

    // Bulk operations
    async bulkUpdateStatus(testCaseIds, status) {
        const promises = testCaseIds.map(id =>
            this.patchTestCase(id, { status })
        );
        return Promise.all(promises);
    }

    async bulkUpdatePriority(testCaseIds, priority) {
        const promises = testCaseIds.map(id =>
            this.patchTestCase(id, { priority })
        );
        return Promise.all(promises);
    }

    // Validation helpers
    validateTestCase(testCaseData) {
        const errors = {};

        if (!testCaseData.testcase_id?.trim()) {
            errors.testcase_id = 'Test Case ID is required';
        }

        // if (!testCaseData.name?.trim()) {
        //     errors.name = 'Name is required';
        // }

        if (!testCaseData.brief?.trim()) {
            errors.brief = 'Brief description is required';
        }

        // if (!testCaseData.details?.trim()) {
        //     errors.details = 'Details are required';
        // }

        // if (!testCaseData.conditions?.trim()) {
        //     errors.conditions = 'Test conditions are required';
        // }

        // if (!testCaseData.procedures?.trim()) {
        //     errors.procedures = 'Test procedures are required';
        // }

        // if (!testCaseData.requirements?.trim()) {
        //     errors.requirements = 'Requirements are required';
        // }

        // if (!testCaseData.estimated_time || testCaseData.estimated_time <= 0) {
        //     errors.estimated_time = 'Estimated time must be greater than 0';
        // }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
}

export default new TestCaseService();
