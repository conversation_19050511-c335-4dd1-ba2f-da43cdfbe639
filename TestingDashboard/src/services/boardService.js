import apiService from './apiService';

class BoardService {
    constructor() {
        this.baseEndpoint = '/api/board';
    }

    // Board Information APIs
    async getBoardInformation(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-information/`, filters);
    }

    async getBoardInformationById(id) {
        return apiService.get(`${this.baseEndpoint}/board-information/${id}/`);
    }

    async createBoardInformation(data) {
        return apiService.post(`${this.baseEndpoint}/board-information/`, data);
    }

    async updateBoardInformation(id, data) {
        return apiService.put(`${this.baseEndpoint}/board-information/${id}/`, data);
    }

    async patchBoardInformation(id, data) {
        return apiService.patch(`${this.baseEndpoint}/board-information/${id}/`, data);
    }

    async deleteBoardInformation(id) {
        return apiService.delete(`${this.baseEndpoint}/board-information/${id}/`);
    }

    // Board HW APIs
    async getBoardHW(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-hw/`, filters);
    }

    async getBoardHWById(id) {
        return apiService.get(`${this.baseEndpoint}/board-hw/${id}/`);
    }

    async createBoardHW(data) {
        return apiService.post(`${this.baseEndpoint}/board-hw/`, data);
    }

    async updateBoardHW(id, data) {
        return apiService.put(`${this.baseEndpoint}/board-hw/${id}/`, data);
    }

    async patchBoardHW(id, data) {
        return apiService.patch(`${this.baseEndpoint}/board-hw/${id}/`, data);
    }

    async deleteBoardHW(id) {
        return apiService.delete(`${this.baseEndpoint}/board-hw/${id}/`);
    }

    // Board Topology APIs
    async getBoardTopology(filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-topology/`, filters);
    }

    async getBoardTopologyById(id) {
        return apiService.get(`${this.baseEndpoint}/board-topology/${id}/`);
    }

    async createBoardTopology(data) {
        return apiService.post(`${this.baseEndpoint}/board-topology/`, data);
    }

    async updateBoardTopology(id, data) {
        return apiService.put(`${this.baseEndpoint}/board-topology/${id}/`, data);
    }

    async patchBoardTopology(id, data) {
        return apiService.patch(`${this.baseEndpoint}/board-topology/${id}/`, data);
    }

    async deleteBoardTopology(id) {
        return apiService.delete(`${this.baseEndpoint}/board-topology/${id}/`);
    }

    // Get choices for dropdowns
    async getChoices() {
        return apiService.get(`${this.baseEndpoint}/choices/`);
    }

    // Search functions
    async searchBoardInformation(searchTerm, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-information/`, {
            search: searchTerm,
            ...filters
        });
    }

    async searchBoardHW(searchTerm, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-hw/`, {
            search: searchTerm,
            ...filters
        });
    }

    async searchBoardTopology(searchTerm, filters = {}) {
        return apiService.get(`${this.baseEndpoint}/board-topology/`, {
            search: searchTerm,
            ...filters
        });
    }

    // Get Board HW by Board Information
    async getBoardHWByBoardInfo(boardInfoId) {
        return apiService.get(`${this.baseEndpoint}/board-hw/`, {
            board_information: boardInfoId
        });
    }

    // Validation helpers
    validateBoardInformation(data) {
        const errors = {};

        if (!data.name?.trim()) {
            errors.name = 'Board name is required';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    validateBoardHW(data) {
        const errors = {};

        if (!data.name?.trim()) {
            errors.name = 'Board HW name is required';
        }

        if (!data.board_information) {
            errors.board_information = 'Board Information is required';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    validateBoardTopology(data) {
        const errors = {};

        if (!data.name?.trim()) {
            errors.name = 'Board Topology name is required';
        }

        if (!data.board_information_ids || data.board_information_ids.length === 0) {
            errors.board_information_ids = 'At least one Board Information is required';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
}

export default new BoardService();
