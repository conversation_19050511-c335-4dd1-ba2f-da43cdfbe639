import { useAuth } from '../contexts/AuthContext';

export const usePermissions = () => {
  const { user, hasPermission, canManageUsers, canManageBuilds, canViewOnly } = useAuth();

  return {
    user,
    hasPermission,
    canManageUsers,
    canManageBuilds,
    canViewOnly,
    isAdmin: user?.role === 'admin',
    isEngineer: user?.role === 'engineer',
    isViewer: user?.role === 'viewer',
  };
};

export default usePermissions;
