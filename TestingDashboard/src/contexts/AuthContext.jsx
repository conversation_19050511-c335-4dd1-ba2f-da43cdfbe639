import React, { createContext, useContext, useState, useEffect } from 'react';
import authService from '../services/authService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in from authService
    const initializeAuth = async () => {
      if (authService.isAuthenticated()) {
        try {
          // Try to get user profile from API
          const profile = await authService.getProfile();
          const userData = {
            id: profile.user?.id || authService.getCurrentUser().id,
            username: profile.username || authService.getCurrentUser().username,
            email: profile.email || authService.getCurrentUser().email,
            fullName: profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
            role: profile.role || 'viewer'
          };
          setUser(userData);
        } catch (error) {
          console.error('Failed to get user profile:', error);
          // Fallback to localStorage data
          const currentUser = authService.getCurrentUser();
          if (currentUser.username) {
            setUser({
              id: currentUser.id,
              username: currentUser.username,
              email: currentUser.email,
              fullName: '',
              role: 'viewer' // Default role
            });
          }
        }
      }
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (username, password) => {
    try {
      // Use authService for authentication
      const userData = await authService.login(username, password);

      // Get user profile to get role information
      const profile = await authService.getProfile();

      const userInfo = {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        fullName: userData.full_name || `${userData.first_name || ''} ${userData.last_name || ''}`.trim(),
        role: profile.role || 'viewer'
      };

      setUser(userInfo);
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message || 'Login failed' };
    }
  };

  // Method to set user data directly (for when authService already handled login)
  const setUserFromAuth = async () => {
    try {
      if (authService.isAuthenticated()) {
        const profile = await authService.getProfile();
        const currentUser = authService.getCurrentUser();

        const userData = {
          id: currentUser.id,
          username: currentUser.username,
          email: currentUser.email,
          fullName: profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
          role: profile.role || 'viewer'
        };

        setUser(userData);
        return { success: true };
      }
      return { success: false, error: 'Not authenticated' };
    } catch (error) {
      console.error('Set user error:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
    setUser(null);
  };

  const hasPermission = (requiredRole) => {
    if (!user) return false;

    const roleHierarchy = {
      'admin': 3,
      'engineer': 2,
      'viewer': 1,
    };

    const userLevel = roleHierarchy[user.role] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  };

  const canManageUsers = () => hasPermission('admin');
  const canManageBuilds = () => hasPermission('engineer');
  const canViewOnly = () => hasPermission('viewer');

  const value = {
    user,
    login,
    logout,
    loading,
    hasPermission,
    canManageUsers,
    canManageBuilds,
    canViewOnly,
    setUserFromAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
