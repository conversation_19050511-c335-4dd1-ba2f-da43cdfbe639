import React from 'react';
import { Navigate } from 'react-router-dom';
import { Box, Typography, Paper } from '@mui/material';
import { Lock as LockIcon } from '@mui/icons-material';
import { usePermissions } from '../hooks/usePermissions';

const AdminRoute = ({ children }) => {
  const { canManageUsers, user } = usePermissions();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (!canManageUsers()) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="60vh"
      >
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center', maxWidth: 400 }}>
          <LockIcon color="error" sx={{ fontSize: 64, mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="textSecondary">
            You don't have permission to access this page. Administrator privileges are required.
          </Typography>
        </Paper>
      </Box>
    );
  }

  return children;
};

export default AdminRoute;
