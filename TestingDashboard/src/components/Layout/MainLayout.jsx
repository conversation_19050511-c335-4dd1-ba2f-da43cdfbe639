import React from 'react';
import { Box, Toolbar } from '@mui/material';
import Sidebar from './Sidebar';

const MainLayout = ({ children }) => {
  return (
    <Box sx={{ display: 'flex' }}>
      <Sidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: 'background.default',
          p: 0,
          minHeight: '100vh',
        }}
      >
        {/* <Toolbar /> */}
        {children}
      </Box>
    </Box>
  );
};

export default MainLayout;
