import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Collapse,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Assignment as AssignmentIcon,
  Computer as ComputerIcon,
  Memory as MemoryIcon,
  AccountTree as TopologyIcon,
  Build as BuildIcon,
  Settings as SettingsIcon,
  PlaylistPlay as TestingPlanIcon,
  People as PeopleIcon,
  ExitToApp as LogoutIcon,
  Api as ApiIcon,
  ExpandLess,
  ExpandMore,
} from '@mui/icons-material';
import AssessmentIcon from '@mui/icons-material/Assessment';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';

const drawerWidth = 240;

const menuItems = [
  { text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard' },
  {
    text: 'Board Setup',
    icon: <ComputerIcon />,
    submenu: [
      { text: 'Board Information', icon: <ComputerIcon />, path: '/board-information' },
      { text: 'Board HW', icon: <MemoryIcon />, path: '/board-hw' },
      { text: 'Board Topology', icon: <TopologyIcon />, path: '/board-topology' },
    ]
  },
  { text: 'Test Cases', icon: <AssignmentIcon />, path: '/testcases' },
  { text: 'Board Configuration', icon: <SettingsIcon />, path: '/board-config' },
  { text: 'Build Configuration', icon: <BuildIcon />, path: '/build-config' },
  { text: 'Testing Plan', icon: <TestingPlanIcon />, path: '/testing-plan-dashboard' },
  { text: 'User Management', icon: <PeopleIcon />, path: '/users' },
  { text: 'Allure Report', icon: <AssessmentIcon />, path: '/allure-report' },
];

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();
  const { canManageUsers } = usePermissions();
  const [openSubmenu, setOpenSubmenu] = useState({});

  const handleNavigation = (path) => {
    navigate(path);
  };

  const handleSubmenuToggle = (itemText) => {
    setOpenSubmenu(prev => ({
      ...prev,
      [itemText]: !prev[itemText]
    }));
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      <Toolbar>
        <Typography variant="h6" noWrap component="div" sx={{ color: 'white' }}>
          Testing Dashboard
        </Typography>
      </Toolbar>
      <Divider sx={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }} />
      <List>
        {menuItems.map((item) => {
          // Hide User Management for non-admin users
          if (item.path === '/users' && !canManageUsers()) {
            return null;
          }

          // Handle items with submenu
          if (item.submenu) {
            return (
              <React.Fragment key={item.text}>
                <ListItem disablePadding>
                  <ListItemButton
                    onClick={() => handleSubmenuToggle(item.text)}
                    sx={{ color: 'white' }}
                  >
                    <ListItemIcon sx={{ color: 'white' }}>
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText primary={item.text} />
                    {openSubmenu[item.text] ? <ExpandLess /> : <ExpandMore />}
                  </ListItemButton>
                </ListItem>
                <Collapse in={openSubmenu[item.text]} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.submenu.map((subItem) => (
                      <ListItem key={subItem.text} disablePadding>
                        <ListItemButton
                          selected={location.pathname === subItem.path}
                          onClick={() => handleNavigation(subItem.path)}
                          sx={{
                            color: 'white',
                            pl: 4 // Indent submenu items
                          }}
                        >
                          <ListItemIcon sx={{ color: 'white' }}>
                            {subItem.icon}
                          </ListItemIcon>
                          <ListItemText primary={subItem.text} />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              </React.Fragment>
            );
          }

          // Handle regular menu items
          return (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
                sx={{ color: 'white' }}
              >
                <ListItemIcon sx={{ color: 'white' }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
      <Divider sx={{ backgroundColor: 'rgba(255, 255, 255, 0.2)', mt: 'auto' }} />
      <List>
        <ListItem disablePadding>
          <ListItemButton onClick={handleLogout} sx={{ color: 'white' }}>
            <ListItemIcon sx={{ color: 'white' }}>
              <LogoutIcon />
            </ListItemIcon>
            <ListItemText primary="Logout" />
          </ListItemButton>
        </ListItem>
      </List>
    </Drawer>
  );
};

export default Sidebar;
