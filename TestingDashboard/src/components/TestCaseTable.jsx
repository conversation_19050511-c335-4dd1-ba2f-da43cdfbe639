import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Alert,
  Snackbar,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { testcaseService } from '../services';

const TestCaseTable = () => {
  const [testCases, setTestCases] = useState([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editingTestCase, setEditingTestCase] = useState(null);
  const [choices, setChoices] = useState({});
  const [formData, setFormData] = useState({
    testcase_id: '',
    name: '',
    brief: '',
    details: '',
    conditions: '',
    test_level: 'System',
    procedures: '',
    requirements: '',
    priority: 'Medium',
    status: 'Active',
    platform_ids: [],
    tags: '',
    estimated_time: 30,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  useEffect(() => {
    loadTestCases();
    loadChoices();
  }, []);

  const loadTestCases = async () => {
    try {
      setLoading(true);
      const data = await testcaseService.getTestCases();
      setTestCases(data.results || data);
    } catch (error) {
      showSnackbar('Error loading test cases: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadChoices = async () => {
    try {
      const data = await testcaseService.getChoices();
      setChoices(data);
    } catch (error) {
      console.error('Error loading choices:', error);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleOpen = (testCase = null) => {
    if (testCase) {
      setEditingTestCase(testCase);
      setFormData({
        testcase_id: testCase.testcase_id,
        name: testCase.name,
        brief: testCase.brief,
        details: testCase.details,
        conditions: testCase.conditions,
        test_level: testCase.test_level,
        procedures: testCase.procedures,
        requirements: testCase.requirements,
        priority: testCase.priority,
        status: testCase.status,
        platform_ids: testCase.platform_ids || [],
        tags: testCase.tags,
        estimated_time: testCase.estimated_time,
      });
    } else {
      setEditingTestCase(null);
      setFormData({
        testcase_id: '',
        name: '',
        brief: '',
        details: '',
        conditions: '',
        test_level: 'System',
        procedures: '',
        requirements: '',
        priority: 'Medium',
        status: 'Active',
        platform_ids: [],
        tags: '',
        estimated_time: 30,
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingTestCase(null);
  };

  const handleSubmit = async () => {
    const validation = testcaseService.validateTestCase(formData);
    if (!validation.isValid) {
      const firstError = Object.values(validation.errors)[0];
      showSnackbar(firstError, 'error');
      return;
    }

    try {
      setLoading(true);
      if (editingTestCase) {
        await testcaseService.updateTestCase(editingTestCase.id, formData);
        showSnackbar('Test case updated successfully');
      } else {
        await testcaseService.createTestCase(formData);
        showSnackbar('Test case created successfully');
      }
      
      handleClose();
      loadTestCases();
    } catch (error) {
      showSnackbar('Error saving test case: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this test case?')) {
      try {
        setLoading(true);
        await testcaseService.deleteTestCase(id);
        showSnackbar('Test case deleted successfully');
        loadTestCases();
      } catch (error) {
        showSnackbar('Error deleting test case: ' + error.message, 'error');
      } finally {
        setLoading(false);
      }
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Critical': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'default';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Inactive': return 'error';
      case 'Draft': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Test Cases</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
          disabled={loading}
        >
          Add Test Case
        </Button>
      </Box>

      {loading && (
        <Box display="flex" justifyContent="center" my={2}>
          <CircularProgress />
        </Box>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Test Case ID</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Test Level</TableCell>
              <TableCell>Estimated Time</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {testCases.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  <Box py={4}>
                    <AssignmentIcon color="disabled" sx={{ fontSize: 48, mb: 2 }} />
                    <Typography variant="body1" color="textSecondary">
                      No test cases found. Create your first test case.
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              testCases.map((testCase) => (
                <TableRow key={testCase.id}>
                  <TableCell>
                    <Chip label={testCase.testcase_id} variant="outlined" />
                  </TableCell>
                  <TableCell>{testCase.name}</TableCell>
                  <TableCell>
                    <Chip
                      label={testCase.priority}
                      color={getPriorityColor(testCase.priority)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={testCase.status}
                      color={getStatusColor(testCase.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{testCase.test_level}</TableCell>
                  <TableCell>{testCase.estimated_time} min</TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleOpen(testCase)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(testCase.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog for Create/Edit */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingTestCase ? 'Edit Test Case' : 'Add New Test Case'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                autoFocus
                label="Test Case ID"
                fullWidth
                variant="outlined"
                value={formData.testcase_id}
                onChange={(e) => setFormData({ ...formData, testcase_id: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Name"
                fullWidth
                variant="outlined"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Brief Description"
                fullWidth
                multiline
                rows={2}
                variant="outlined"
                value={formData.brief}
                onChange={(e) => setFormData({ ...formData, brief: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !formData.testcase_id.trim() || !formData.name.trim()}
          >
            {editingTestCase ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TestCaseTable;
