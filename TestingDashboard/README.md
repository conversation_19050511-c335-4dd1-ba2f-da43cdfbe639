# Testing Dashboard

A Material-UI dashboard for managing testing plans and triggering Yocto build images via <PERSON>.

## Features

- **Authentication System**: Role-based login with demo credentials
- **User Management**: Admin-only user management with role-based permissions
- **Test Case Management**: Create, edit, and manage test cases with platform specifications
- **Board Setup Management**: Configure board specifications with JSON configuration
- **Build Configuration**: Create build configurations combining boards and test cases
- **Jenkins Integration**: Trigger Yocto builds via Jenkins API
- **Role-Based Access Control**: Different permissions for Admin, Engineer, and Viewer roles
- **Responsive Design**: Mobile-friendly interface using Material-UI

## Demo Credentials

### Administrator Account
- Username: `admin`
- Password: `admin`
- Role: Administrator (full access to all features including user management)

### Demo Users (created automatically)
- **Engineer**: `engineer1` / `password` (can manage builds and configurations)
- **Viewer**: `viewer1` / `password` (read-only access)

## User Roles and Permissions

- **Administrator**: Full access to all features including user management
- **Engineer**: Can manage test cases, boards, and build configurations
- **Viewer**: Read-only access to view data and dashboards

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment configuration:
   ```bash
   cp .env.example .env
   ```

4. Update `.env` with your Jenkins configuration:
   ```
   REACT_APP_JENKINS_URL=http://your-jenkins-server:8080
   REACT_APP_JENKINS_USERNAME=your-username
   REACT_APP_JENKINS_TOKEN=your-api-token
   ```

5. Start the development server:
   ```bash
   npm run dev
   ```

## Usage

### Test Cases
- Navigate to "Test Cases" to manage your test scenarios
- Each test case includes: name, description, and target platform
- Supported platforms: ARM64, x86_64, ARM32, RISC-V

### Board Setup
- Navigate to "Board Setup" to configure hardware boards
- Configuration is stored in JSON format
- Include specifications like CPU, memory, storage, network, etc.

### Build Configuration
- Navigate to "Build Configuration" to create build setups
- Select a board and multiple test cases
- Choose the corresponding Jenkins job
- Trigger builds directly from the interface

### User Management (Admin Only)
- Navigate to "User Management" to manage system users
- Create, edit, and delete user accounts
- Assign roles and manage permissions
- Toggle user status (active/inactive)
- View user login history

### Jenkins Integration
- The application simulates Jenkins API calls for demonstration
- In production, update `src/services/jenkinsService.js` with actual API calls
- Ensure Jenkins is configured with appropriate jobs for each platform

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Layout/         # Layout components (Sidebar, MainLayout)
│   ├── ErrorBoundary.jsx
│   ├── LoadingSpinner.jsx
│   └── ProtectedRoute.jsx
├── contexts/           # React contexts
│   └── AuthContext.jsx
├── hooks/              # Custom hooks
│   └── useLocalStorage.js
├── pages/              # Page components
│   ├── Dashboard.jsx
│   ├── Login.jsx
│   ├── TestCases.jsx
│   ├── BoardSetup.jsx
│   ├── BuildConfiguration.jsx
│   └── UserManagement.jsx
├── services/           # API services
│   └── jenkinsService.js
├── theme.js            # Material-UI theme
├── App.jsx             # Main application component
└── main.jsx            # Application entry point
```

## Technologies Used

- **React 19**: Frontend framework
- **Material-UI (MUI)**: UI component library
- **React Router**: Client-side routing
- **Axios**: HTTP client for API calls
- **Vite**: Build tool and development server

## Development

### Available Scripts

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run preview`: Preview production build
- `npm run lint`: Run ESLint

### Data Storage

The application uses localStorage for data persistence in demo mode. In production, you would integrate with a backend API for proper data management.

## Jenkins Configuration

For production use, ensure your Jenkins server has:

1. Jobs configured for each platform (ARM64, x86_64, ARM32, RISC-V)
2. API access enabled
3. Proper authentication tokens
4. Build parameters configured to accept board and test case information

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
