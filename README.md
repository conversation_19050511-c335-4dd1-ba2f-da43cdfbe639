# Dashboard_CI


## Project Stack

- Front-End: React.js + Vite
- Back-End: Django
- Database: ProgreSQL
- Deploy: Gunicorn + Nginx

## How to deploy backend at local

- Requires python3.10 or above
- Clone source code from repo dashboard_ci
- Create virtual environment
- Make migrations to update models and databases
- Run Django server at port 8000

```
git clone http://**************:9090/root/dashboard_ci.git
cd dashboard_ci
git checkout main

cd TestingDashboard_django
python3 -m venv .venv
source .venv/bin/activate
python3 -m pip install requirements.txt

cd TestingDashboard
python3 manage.py makemigrations
python3 manage.py migrate
python3 manage.py runserver 0.0.0.0:8000
```

## How to deploy frontend at local

- Requires node.js v18.0 or above
- Clone source code from dashboard_ci repo
- Update backend url in .env and .env.development
- Install dependencies
- Run React.js at port 5173

```
git clone http://**************:9090/root/dashboard_ci.git
cd dashboard_ci
git checkout main

cd TestingDashboard
npm install
npx vite --port=5173 --host
```

## How to deploy backend at server machine

- Create a folder config/gunicorn:
```
cd TestingDashboard_django/TestingDashboard
mkdir -pv config/gunicorn
```
- Create a script ```config/gunicorn/dev.py``` with content:
```
"""Gunicorn *development* config file"""

# Django WSGI application path in pattern MODULE_NAME:VARIABLE_NAME
wsgi_app = "TestingDashboard.wsgi:application"
# The granularity of Error log outputs
loglevel = "debug"
# The number of worker processes for handling requests
workers = 2
# The socket to bind
bind = "0.0.0.0:8000"
# Restart workers when code changes (development only!)
reload = True
# Write access and error info to /var/log
accesslog = "/backup/meihk/log/gunicorn/access_dashboard.log"
errorlog = "/backup/meihk/log/gunicorn/error_dashboard.log"
# Redirect stdout/stderr to log file
capture_output = True
# PID file so you can easily fetch process ID
pidfile = "/backup/meihk/log/gunicorn/dev_dashboard.pid"
# Daemonize the Gunicorn process (detach & enter background)
daemon = True
```
- To start gunicorn: ```gunicorn -c config/gunicorn/dev.py```
- Create a service to run gunicorn:
```sudo nano /etc/systemd/system/gunicorn-testingdashboard.service```
- Add content:
```
[Unit]
Description=Gunicorn for TestingDashboard Django project
After=network.target

[Service]
User=jenkins
Group=www-data
WorkingDirectory=/backup/meihk/dashboard_ci/TestingDashboard_django/TestingDashboard
ExecStart=/backup/meihk/dashboard_ci/TestingDashboard_django/venv/bin/gunicorn \
    --access-logfile - \
    --workers 3 \
    --bind unix:/backup/meihk/log/gunicorn/gunicorn_TestingDashboard.sock \
    TestingDashboard.wsgi:application
Restart=always
RestartSec=3

UMask=007

[Install]
WantedBy=multi-user.target
```
- Give access log folder for gunicorn:
```
sudo mkdir -p /backup/meihk/log/gunicorn
sudo chown jenkins:www-data /backup/meihk/log/gunicorn
sudo chmod 770 /backup/meihk/log/gunicorn
```
- Reload systemctl
```
sudo systemctl daemon-reload
sudo systemctl enable gunicorn-testingdashboard
sudo systemctl start gunicorn-testingdashboard
```

## How to deploy frontent at server machine

- Install nginx: ```sudo apt-get install nginx```
- Go to dashboard_ci/TestingDashboard: ```cd TestingDashboard```
- Create a copyfile ```.env.production``` from ```.env.development``` and change environment in ```.env``` to production
- Build project: ```npm run build```
- Go to /etc/nginx/site-available: ```cd /etc/nginx/site-available```
- Rename default file to testingdashboard: ```mv default testingdashboard```
- Edit testingdashboard file with content:
```
server_tokens off;
access_log    /backup/meihk/log/nginx/access.log;
error_log     /backup/meihk/log/nginx/error.log;

server {
        listen 5173 default_server;
        listen [::]:5173 default_server;
        server_name _;

        root /backup/meihk/dashboard_ci/TestingDashboard/dist;
        index index.html;

        location / {
                try_files $uri $uri/ /index.html;
        }

        location /static/ {
                autoindex on;
                alias /backup/meihk/dashboard_ci/TestingDashboard_django/TestingDashboard/staticfiles/;
        }

        location /auth-api/ {
                proxy_pass http://unix:/backup/meihk/log/gunicorn/gunicorn_TestingDashboard.sock;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/ {
                proxy_pass http://unix:/backup/meihk/log/gunicorn/gunicorn_TestingDashboard.sock;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }
}
```
- Make sure site is enabled: ```sudo ln -sf /etc/nginx/sites-available/testingdashboard /etc/nginx/sites-enabled/```
- Remove default site: ```sudo rm /etc/nginx/sites-enabled/default```
- Reload nginx: 
```
sudo nginx -t
sudo systemctl reload nginx
```
- Enjoy the result
